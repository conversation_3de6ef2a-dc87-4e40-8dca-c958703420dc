package com.terheyden.mud.mudlib.rooms

import com.terheyden.mud.corelib.Direction
import com.terheyden.mud.corelib.room.Room
import com.terheyden.mud.corelib.room.RoomFeature
import com.terheyden.mud.mudlib.items.Torch
import com.terheyden.mud.mudlib.npcs.GiantRat
import org.springframework.stereotype.Component

/**
 * A dark underground tunnel beneath the tower, filled with danger and mystery.
 */
@Component
class UndergroundTunnel : Room(
    id = "underground_tunnel",
    name = "Underground Tunnel",
    description = "You find yourself in a narrow, damp tunnel carved from solid rock. " +
            "The air is thick and musty, and water drips steadily from the ceiling above. " +
            "Strange phosphorescent moss clings to the walls, providing just enough light to see. " +
            "The tunnel stretches into darkness in both directions, and you can hear the " +
            "scurrying of small creatures echoing from the shadows.",
    features = listOf(
        RoomFeature(
            id = "phosphorescent_moss",
            names = listOf("phosphorescent moss", "moss", "glowing moss", "green moss"),
            description = "The moss glows with a faint green light, pulsing gently like a slow heartbeat. " +
                    "It feels slightly warm to the touch and seems to react to your presence by " +
                    "glowing brighter. This natural bioluminescence is the only thing keeping " +
                    "the tunnel from being completely dark.",
            keywords = listOf("green", "light", "pulsing", "heartbeat", "warm", "bioluminescence", "dark")
        ),
        RoomFeature(
            id = "dripping_water",
            names = listOf("dripping water", "water", "drops", "ceiling"),
            description = "Water constantly drips from the rough stone ceiling, creating small puddles " +
                    "on the tunnel floor. The sound echoes eerily through the passage, and the " +
                    "water tastes of minerals and earth. Each drop seems to mark time in this " +
                    "timeless underground realm.",
            keywords = listOf("stone", "puddles", "echoes", "minerals", "earth", "time", "timeless")
        ),
        RoomFeature(
            id = "carved_walls",
            names = listOf("carved walls", "walls", "rock walls", "stone walls"),
            description = "The tunnel walls show clear signs of being carved by tools, not natural erosion. " +
                    "The chisel marks are still visible in places, suggesting this passage was " +
                    "created with purpose. Strange symbols are occasionally visible, worn smooth " +
                    "by time and moisture.",
            keywords = listOf("tools", "erosion", "chisel", "marks", "purpose", "symbols", "worn", "moisture")
        ),
    ),
    exits = mapOf(
        Direction.NORTH to DungeonChamber::class,
        Direction.SOUTH to TowerBasement::class,
    ),
    items = listOf(
        Torch(),
        GiantRat(),
    ),
) {

    override fun onPlayerEnter(playerId: String) {
        // The tunnel could have atmospheric effects
        // - Random sounds of creatures
        // - Chance of getting lost without light
        // - Environmental hazards
    }
}

package com.terheyden.mud.mudlib.rooms

import com.terheyden.mud.corelib.Direction
import com.terheyden.mud.corelib.puzzle.PuzzleRoom
import com.terheyden.mud.corelib.puzzle.PuzzleState
import com.terheyden.mud.corelib.room.RoomFeature
import com.terheyden.mud.mudlib.items.AncientScroll
import com.terheyden.mud.mudlib.items.MagicalTome
import com.terheyden.mud.mudlib.puzzles.LibraryRiddlePuzzle
import org.springframework.stereotype.Component

/**
 * An ancient library filled with mystical knowledge and riddle-based puzzles.
 * Players must solve riddles posed by the magical books to unlock the library's secrets.
 */
@Component
class AncientLibrary : PuzzleRoom(
    id = "ancient_library",
    name = "Ancient Library",
    description = "You enter a vast library with towering shelves that stretch up into shadows. " +
            "Ancient tomes and scrolls line the walls, their leather bindings cracked with age. " +
            "Floating candles provide a warm, flickering light that dances across the countless " +
            "volumes. In the center of the room stands a magnificent reading desk made of dark " +
            "oak, upon which rests an open book that seems to glow with inner light. The air " +
            "smells of old parchment, ink, and something faintly magical.",
    features = listOf(
        RoomFeature(
            id = "towering_shelves",
            names = listOf("towering shelves", "shelves", "bookshelves", "tall shelves"),
            description = "The shelves reach impossibly high, disappearing into the shadows above. " +
                    "They are carved from ancient wood and filled with thousands of books, scrolls, " +
                    "and manuscripts. Some books seem to shimmer with magical energy, while others " +
                    "appear to be bound in materials you don't recognize.",
            keywords = listOf("impossibly", "high", "shadows", "carved", "ancient", "thousands", "manuscripts", "shimmer", "energy", "materials")
        ),
        RoomFeature(
            id = "floating_candles",
            names = listOf("floating candles", "candles", "magical candles", "flickering candles"),
            description = "Dozens of candles float freely through the air, their flames never " +
                    "wavering despite having no visible means of support. They cast a warm, " +
                    "golden light that seems to follow you as you move, ensuring you can always " +
                    "see clearly. The wax never seems to drip or diminish.",
            keywords = listOf("dozens", "freely", "flames", "wavering", "visible", "support", "golden", "follow", "clearly", "drip", "diminish")
        ),
        RoomFeature(
            id = "reading_desk",
            names = listOf("reading desk", "desk", "oak desk", "magnificent desk"),
            description = "A beautiful desk carved from a single piece of dark oak, its surface " +
                    "polished to a mirror shine. Intricate patterns of vines and leaves are " +
                    "carved around its edges, and small drawers with brass handles line its sides. " +
                    "The desk seems to radiate an aura of scholarly wisdom.",
            keywords = listOf("beautiful", "carved", "single", "piece", "polished", "mirror", "shine", "intricate", "patterns", "vines", "leaves", "drawers", "brass", "handles", "radiate", "scholarly", "wisdom")
        ),
        RoomFeature(
            id = "glowing_book",
            names = listOf("glowing book", "book", "open book", "magical book", "riddle book"),
            description = "An ancient tome lies open on the reading desk, its pages glowing with " +
                    "soft, blue-white light. The text seems to shift and change as you watch, " +
                    "and you can sense that this book contains great wisdom - but only for those " +
                    "clever enough to unlock its secrets through riddles and puzzles.",
            keywords = listOf("ancient", "tome", "pages", "blue-white", "shift", "change", "watch", "sense", "wisdom", "clever", "unlock", "secrets", "riddles", "puzzles")
        ),
    ),
    exits = mapOf(
        Direction.SOUTH to TowerChamber::class,
        Direction.EAST to RunicCircle::class,
    ),
    items = listOf(
        // Items will be revealed when puzzle is solved
    ),
) {

    private val riddlePuzzle = LibraryRiddlePuzzle()

    override fun handlePuzzleInteraction(playerId: String, action: String, target: String): String? {
        return when (action.lowercase()) {
            "say", "answer", "speak" -> handleRiddleAnswer(target)
            "examine" -> when {
                target.contains("book") || target.contains("riddle") -> getCurrentRiddleText()
                else -> null
            }
            "use" -> when {
                target.contains("book") || target.contains("desk") -> getCurrentRiddleText()
                else -> null
            }
            else -> null
        }
    }

    private fun handleRiddleAnswer(answer: String): String {
        if (puzzleState == PuzzleState.SOLVED) {
            return "The riddles have already been solved. The library's secrets are now open to you."
        }

        val result = riddlePuzzle.checkAnswer(answer)

        return when (result) {
            LibraryRiddlePuzzle.PuzzleResult.CORRECT_CONTINUE -> {
                puzzleState = PuzzleState.IN_PROGRESS
                "Correct! The book glows brighter and the pages turn to reveal the next riddle.\n\n" +
                        getCurrentRiddleText()
            }
            LibraryRiddlePuzzle.PuzzleResult.COMPLETE -> {
                onPuzzleSolved("player")
                revealTreasures()
                "Excellent! You have answered all the riddles correctly. The book closes with a " +
                        "satisfied whisper, and you hear the sound of hidden compartments opening " +
                        "throughout the library. Ancient knowledge and treasures are now yours to discover!"
            }
            LibraryRiddlePuzzle.PuzzleResult.INCORRECT -> {
                "That is not correct. The book dims slightly, showing its disappointment. " +
                        "You have ${riddlePuzzle.getAttemptsRemaining()} attempts remaining. " +
                        "Would you like a hint? Try 'examine book' for guidance."
            }
            LibraryRiddlePuzzle.PuzzleResult.FAILED -> {
                onPuzzleFailed("player")
                riddlePuzzle.reset()
                "The book snaps shut with an angry flutter of pages. You have failed the test of " +
                        "wisdom. The riddles have reset - you may try again if you dare."
            }
            LibraryRiddlePuzzle.PuzzleResult.ALREADY_COMPLETE -> {
                "The riddles have already been completed."
            }
        }
    }

    private fun getCurrentRiddleText(): String {
        val riddle = riddlePuzzle.getCurrentRiddle()
        return if (riddle != null) {
            "The glowing book presents you with this riddle:\n\n" +
                    "\"${riddle.question}\"\n\n" +
                    "Speak your answer aloud to respond. (Attempts remaining: ${riddlePuzzle.getAttemptsRemaining()})"
        } else {
            "The book has no more riddles to offer. Its wisdom has been unlocked."
        }
    }

    private fun revealTreasures() {
        // Add items that become available when puzzle is solved
        addItem(MagicalTome())
        addItem(AncientScroll())
    }

    override fun getSolvedDescription(): String {
        return "The library now radiates with unlocked wisdom. Hidden compartments have opened, " +
                "revealing ancient treasures and scrolls of knowledge."
    }

    override fun resetPuzzle() {
        super.resetPuzzle()
        riddlePuzzle.reset()
        // Remove revealed items
        removeItem("magical_tome")
        removeItem("ancient_scroll")
    }

    override fun onPlayerEnter(playerId: String) {
        // Provide initial guidance when entering
        if (puzzleState == PuzzleState.UNSOLVED) {
            // This could trigger a message about the glowing book
        }
    }
}

package com.terheyden.mud.mudlib.npcs

import com.terheyden.mud.corelib.living.NPC
import com.terheyden.mud.corelib.living.NPCType
import com.terheyden.mud.corelib.living.Player
import com.terheyden.mud.mudlib.items.GoldCoins
import com.terheyden.mud.mudlib.items.SilverRing

class JewelryMerchant : NPC(
    id = "jewelry_merchant",
    name = "jewelry merchant",
    description = "An elegantly dressed merchant specializing in fine jewelry and precious items.",
    inventory = listOf(SilverRing(), GoldCoins()),
    maxHealthPoints = 70,
    currentHealthPoints = 70,
    level = 2,
    baseAttackPower = 4,
    baseDefense = 8,
    npcType = NPCType.FRIENDLY,
    experienceReward = 0,
    lootTable = emptyList(),
) {
    override fun getGreeting(player: Player): String {
        return "The jewelry merchant smiles warmly. 'Welcome! I have the finest jewelry in the region.'"
    }

    override fun handleDialogue(player: Player, message: String): String {
        return "The jewelry merchant nods politely. 'How may I assist you with fine jewelry today?'"
    }
}

package com.terheyden.mud.mudlib.rooms

import com.terheyden.mud.corelib.Direction
import com.terheyden.mud.corelib.room.Room
import org.springframework.stereotype.Component

/**
 * Peaceful forest path.
 */
@Component
class ForestPath : Room(
    id = "forest_path",
    name = "Forest Path",
    description = "You're walking along a secluded dirt path, winding through a quiet forest. " +
            "Birds are chirping overhead, and the occasional rustle of leaves alerts you to the presence " +
            "of wildlife. To the south, you can see the village square.",
    features = listOf(
    ),
    exits = mapOf(
        Direction.NORTH to ForestClearing::class,
        Direction.SOUTH to VillageSquare::class,
    ),
)

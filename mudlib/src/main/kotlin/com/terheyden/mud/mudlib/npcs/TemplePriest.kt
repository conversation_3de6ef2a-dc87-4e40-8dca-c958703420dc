package com.terheyden.mud.mudlib.npcs

import com.terheyden.mud.corelib.living.NPC
import com.terheyden.mud.corelib.living.NPCType
import com.terheyden.mud.corelib.living.Player
import com.terheyden.mud.mudlib.items.BlessedCandle
import com.terheyden.mud.mudlib.items.GoldCoins
import com.terheyden.mud.mudlib.items.HealingPotion
import com.terheyden.mud.mudlib.items.HolyWater
import com.terheyden.mud.mudlib.items.PrayerBook

/**
 * A wise temple priest who offers healing, blessings, and spiritual guidance.
 */
class TemplePriest : NPC(
    id = "temple_priest",
    name = "temple priest",
    description = "A serene elderly priest with gentle eyes and a warm smile. He wears simple " +
            "white robes tied with a golden cord, and a holy symbol hangs from his neck. " +
            "His hands are soft but strong, clearly accustomed to the laying on of hands " +
            "for healing. There's an aura of peace and wisdom about him that makes you " +
            "feel safe and welcome in his presence.",
    inventory = listOf(
        HealingPotion(), HealingP<PERSON>(), He<PERSON><PERSON><PERSON>(),
        <PERSON><PERSON><PERSON><PERSON>(), <PERSON><PERSON><PERSON><PERSON>(),
        <PERSON><PERSON><PERSON>(),
        HolyWater(), HolyWater(),
        GoldCoins()
    ),
    maxHealthPoints = 100,
    currentHealthPoints = 100,
    level = 6,
    baseAttackPower = 4, // Very peaceful
    baseDefense = 15, // Protected by divine favor
    npcType = NPCType.FRIENDLY,
    experienceReward = 0,
    lootTable = emptyList(),
) {

    private var hasBlessed = false
    private var healingCount = 0

    override fun getGreeting(player: Player): String {
        return "The priest looks up from his prayers and smiles warmly. 'Peace be with you, " +
                "child. Welcome to this sacred place. I am Father Benedict, servant of the " +
                "divine light. How may I help you today? Are you in need of healing, blessing, " +
                "or perhaps spiritual guidance?'"
    }

    override fun handleDialogue(player: Player, message: String): String {
        val lowerMessage = message.lowercase()

        return when {
            lowerMessage.contains("hello") || lowerMessage.contains("greetings") || lowerMessage.contains("peace") -> {
                getGreeting(player)
            }

            lowerMessage.contains("heal") || lowerMessage.contains("healing") -> {
                healingCount++
                when {
                    player.currentHealthPoints >= player.maxHealthPoints -> {
                        "Father Benedict examines you with kind eyes. 'I sense no wounds upon " +
                                "you, child. Your body is whole and healthy. Perhaps you seek " +
                                "healing of a different kind - of the spirit or the heart?'"
                    }

                    player.currentHealthPoints < player.maxHealthPoints / 4 -> {
                        // Severely wounded
                        player.heal(player.maxHealthPoints) // Full heal for severe wounds
                        "Father Benedict's eyes widen with concern. 'By the light! You are " +
                                "gravely wounded, child!' He places his hands upon you and speaks " +
                                "words of power. Divine light flows through you, mending your " +
                                "wounds completely. 'There, you are restored. Go in peace, but " +
                                "be more careful in your travels.'"
                    }

                    else -> {
                        // Moderate wounds
                        val healAmount = (player.maxHealthPoints * 0.75).toInt()
                        player.heal(healAmount)
                        "Father Benedict places his hands gently upon your wounds and murmurs " +
                                "a prayer. Warm, golden light flows from his touch, easing your " +
                                "pain and mending your injuries. 'The divine light heals you, " +
                                "child. May you use this gift wisely.'"
                    }
                }
            }

            lowerMessage.contains("bless") || lowerMessage.contains("blessing") -> {
                if (!hasBlessed) {
                    hasBlessed = true
                    "Father Benedict raises his hands and speaks words of blessing. 'May the " +
                            "divine light guide your path, protect you from harm, and grant you " +
                            "wisdom in your choices. May your heart remain pure and your purpose " +
                            "true. Go forth with this blessing, child.' You feel a warm, " +
                            "protective aura settle around you."
                } else {
                    "Father Benedict smiles gently. 'You already carry my blessing, child. " +
                            "The divine light continues to watch over you. Live righteously " +
                            "and the blessing will remain strong.'"
                }
            }

            lowerMessage.contains("guidance") || lowerMessage.contains("advice") || lowerMessage.contains("wisdom") -> {
                val guidanceMessages = listOf(
                    "Father Benedict speaks thoughtfully. 'Remember, child, that true strength " +
                            "comes not from the sword, but from compassion and understanding. " +
                            "Help those in need, and you will find your own path illuminated.'",
                    "Father Benedict nods sagely. 'In times of darkness, look for the light " +
                            "within yourself and others. Even the smallest candle can drive " +
                            "away the deepest shadow. Be that light for others.'",
                    "Father Benedict's eyes twinkle with wisdom. 'The greatest treasures are " +
                            "not gold or gems, but the bonds of friendship and the peace that " +
                            "comes from doing what is right. Seek these treasures above all others.'",
                    "Father Benedict speaks gently. 'When faced with difficult choices, ask " +
                            "yourself: will this action bring more light or darkness into the " +
                            "world? Let that be your guide, and you will not go astray.'"
                )
                guidanceMessages.random()
            }

            lowerMessage.contains("prayer") || lowerMessage.contains("pray") -> {
                "Father Benedict gestures to the altar. 'You are always welcome to pray here, " +
                        "child. The divine light hears all sincere prayers, whether spoken " +
                        "aloud or whispered in the heart. Kneel before the altar if you wish, " +
                        "or simply speak your thoughts to the light.'"
            }

            lowerMessage.contains("temple") || lowerMessage.contains("church") -> {
                "Father Benedict spreads his arms wide. 'This temple has stood for over two " +
                        "hundred years, a beacon of hope and healing for all who enter. These " +
                        "walls have witnessed countless prayers answered, wounds healed, and " +
                        "souls comforted. It is a sacred place, open to all who come in peace.'"
            }

            lowerMessage.contains("village") || lowerMessage.contains("people") -> {
                "Father Benedict's face brightens. 'The people of this village are good folk - " +
                        "hardworking, honest, and caring. They support each other in times of " +
                        "need and celebrate together in times of joy. I am blessed to serve " +
                        "such a community.'"
            }

            lowerMessage.contains("evil") || lowerMessage.contains("darkness") || lowerMessage.contains("danger") -> {
                "Father Benedict's expression grows serious. 'There are indeed dark forces " +
                        "in the world, child, but remember that light always triumphs over " +
                        "darkness in the end. Stay true to your principles, help others, and " +
                        "the light will protect you even in the darkest places.'"
            }

            lowerMessage.contains("donation") || lowerMessage.contains("offering") || lowerMessage.contains("tithe") -> {
                "Father Benedict bows his head humbly. 'The temple gratefully accepts any " +
                        "offerings you wish to make, but know that your good deeds and kind " +
                        "heart are worth more than gold. Give what you can, when you can, " +
                        "and the divine light will bless your generosity.'"
            }

            lowerMessage.contains("goodbye") || lowerMessage.contains("farewell") -> {
                "Father Benedict raises his hand in blessing. 'Go in peace, child. May the " +
                        "divine light guide your steps and protect you on your journey. You " +
                        "are always welcome to return to this sacred place.'"
            }

            else -> {
                val responses = listOf(
                    "Father Benedict listens with patient attention. 'Please, tell me more.'",
                    "Father Benedict nods with understanding. 'I see. Continue, child.'",
                    "Father Benedict's eyes show deep compassion. 'Your words touch my heart.'",
                    "Father Benedict speaks gently. 'The divine light hears all things, child.'",
                    "Father Benedict smiles peacefully. 'Speak freely here. This is a place of sanctuary.'"
                )
                responses.random()
            }
        }
    }
}

package com.terheyden.mud.mudlib.npcs

import com.terheyden.mud.corelib.living.NPC
import com.terheyden.mud.corelib.living.NPCType
import com.terheyden.mud.corelib.living.Player
import com.terheyden.mud.mudlib.items.MapOfRegion
import com.terheyden.mud.mudlib.items.SkillBook
import com.terheyden.mud.mudlib.items.VillageHistory

class VillageLibrarian : NPC(
    id = "village_librarian",
    name = "village librarian",
    description = "A knowledgeable librarian who maintains the village's collection of books.",
    inventory = listOf(VillageHistory(), MapOfRegion(), SkillBook()),
    maxHealthPoints = 70,
    currentHealthPoints = 70,
    level = 3,
    baseAttackPower = 3,
    baseDefense = 6,
    npcType = NPCType.FRIENDLY,
    experienceReward = 0,
    lootTable = emptyList(),
) {
    override fun getGreeting(player: Player): String {
        return "The librarian looks up from cataloging books. 'Welcome to our library! How may I help you find knowledge today?'"
    }

    override fun handleDialogue(player: Player, message: String): String {
        return "The librarian adjusts their spectacles. 'Knowledge is the greatest treasure of all.'"
    }
}

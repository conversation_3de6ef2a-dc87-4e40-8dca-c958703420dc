package com.terheyden.mud.mudlib.npcs

import com.terheyden.mud.corelib.living.NPC
import com.terheyden.mud.corelib.living.NPCType
import com.terheyden.mud.corelib.living.Player
import com.terheyden.mud.mudlib.items.EnchantedFlower
import com.terheyden.mud.mudlib.items.MagicalBerries

/**
 * A mystical forest spirit that protects the enchanted grove.
 */
class ForestSpirit : NPC(
    id = "forest_spirit",
    name = "forest spirit",
    description = "A ethereal being of pure nature magic, appearing as a shimmering figure " +
            "made of leaves, flowers, and starlight. The spirit's form constantly shifts " +
            "and changes, sometimes appearing as a graceful woman, other times as a " +
            "majestic tree, and occasionally as a swirling vortex of natural energy. " +
            "Its presence fills the grove with peace and wonder.",
    maxHealthPoints = 120,
    currentHealthPoints = 120,
    level = 6,
    baseAttackPower = 8, // Peaceful but powerful if threatened
    baseDefense = 15,
    npcType = NPCType.NEUTRAL,
    experienceReward = 0, // Attacking nature spirits brings no honor
    lootTable = listOf(EnchantedFlower(), MagicalBerries()),
) {

    private var hasBlessed = false
    private var trustLevel = 0

    override fun getGreeting(player: Player): String {
        return "The forest spirit's voice whispers like wind through leaves: 'Welcome, " +
                "child of the mortal realm. You stand in a place of ancient magic. " +
                "Speak your intentions, and let the grove judge your heart.'"
    }

    override fun handleDialogue(player: Player, message: String): String {
        val lowerMessage = message.lowercase()

        return when {
            lowerMessage.contains("hello") || lowerMessage.contains("greetings") -> {
                trustLevel++
                "The spirit's form shimmers with approval. 'Politeness is a virtue rare among " +
                        "mortals. You show respect for the old ways.'"
            }
            
            lowerMessage.contains("nature") || lowerMessage.contains("forest") || lowerMessage.contains("trees") -> {
                trustLevel += 2
                "The spirit's voice grows warmer, like sunlight through leaves. 'You understand " +
                        "the importance of the natural world. The trees whisper that you have " +
                        "a good heart. Nature recognizes its friends.'"
            }
            
            lowerMessage.contains("magic") || lowerMessage.contains("power") -> {
                "Starlight swirls around the spirit as it speaks. 'Magic flows through all " +
                        "living things, connecting every leaf, every stone, every breath. " +
                        "True power comes not from domination, but from harmony with the " +
                        "natural order.'"
            }
            
            lowerMessage.contains("blessing") || lowerMessage.contains("help") -> {
                if (trustLevel >= 3 && !hasBlessed) {
                    hasBlessed = true
                    val flower = EnchantedFlower()
                    player.currentRoom.addItem(flower)
                    "The spirit extends a hand made of woven light and flowers. 'You have shown " +
                            "respect for nature and its ways. Accept this gift - an enchanted " +
                            "flower that will aid you in your journeys. May it remind you that " +
                            "magic and nature are one.'"
                } else if (hasBlessed) {
                    "The spirit has already blessed you, child. Use the gift wisely."
                } else {
                    "The spirit studies you carefully. 'Trust must be earned through words and " +
                            "deeds. Show me that you understand the value of the natural world.'"
                }
            }
            
            lowerMessage.contains("grove") || lowerMessage.contains("place") -> {
                "This grove is a sanctuary where the veil between worlds grows thin. Here, " +
                        "the magic of the earth flows freely, and those who respect nature " +
                        "may find healing and wisdom. But beware - those who would harm " +
                        "this place will face nature's wrath."
            }
            
            lowerMessage.contains("danger") || lowerMessage.contains("evil") -> {
                "The spirit's form darkens like storm clouds. 'Dark forces stir in the deep " +
                        "places of the earth. The balance is threatened by those who would " +
                        "corrupt nature's gifts. The grove's magic grows stronger to resist " +
                        "this darkness, but vigilance is needed.'"
            }
            
            lowerMessage.contains("wisdom") || lowerMessage.contains("knowledge") -> {
                trustLevel++
                val wisdom = listOf(
                    "Remember: every action ripples through the web of life.",
                    "The strongest trees bend with the wind rather than break against it.",
                    "In nature, nothing is wasted - even death feeds new life.",
                    "True strength comes from understanding your place in the greater whole.",
                    "Listen to the whispers of the wind - it carries messages from distant places."
                )
                "The spirit's voice carries the weight of centuries: '${wisdom.random()}'"
            }
            
            lowerMessage.contains("goodbye") || lowerMessage.contains("farewell") -> {
                "The spirit's form begins to fade like morning mist. 'Go in peace, child of " +
                        "the mortal realm. May the light of the grove guide your path, and " +
                        "may you always remember the lessons of the natural world.'"
            }
            
            else -> {
                val responses = listOf(
                    "The spirit listens with the patience of ancient trees.",
                    "Flowers bloom in the spirit's wake as it considers your words.",
                    "The spirit's form shifts like leaves in a gentle breeze.",
                    "Starlight dances around the spirit as it contemplates your meaning.",
                    "The grove itself seems to lean in, listening to your conversation."
                )
                responses.random()
            }
        }
    }

    override fun onPlayerEnter(player: Player): String {
        return "As you step into the enchanted grove, the air shimmers and a figure of pure " +
                "natural magic materializes before you. The forest spirit regards you with " +
                "ancient, knowing eyes that seem to see into your very soul."
    }

    override fun getExamineDescription(): String {
        return buildString {
            append(super.getExamineDescription())
            appendLine()
            appendLine("This being is nature incarnate - a guardian of the old ways and " +
                    "protector of the grove's ancient magic. Its very presence makes the " +
                    "flowers bloom brighter and the trees whisper with contentment.")
            if (trustLevel >= 3) {
                appendLine("The spirit seems to trust you and view you as a friend of nature.")
            } else {
                appendLine("The spirit watches you carefully, judging your intentions.")
            }
        }
    }
}

package com.terheyden.mud.mudlib.npcs

import com.terheyden.mud.corelib.living.NPC
import com.terheyden.mud.corelib.living.NPCType
import com.terheyden.mud.corelib.living.Player
import com.terheyden.mud.mudlib.items.GoldCoins
import com.terheyden.mud.mudlib.items.SkillBook

class ScholarlyVisitor : NPC(
    id = "scholarly_visitor",
    name = "scholarly visitor",
    description = "A traveling scholar researching local history and lore.",
    inventory = listOf(SkillBook(), GoldCoins()),
    maxHealthPoints = 60,
    currentHealthPoints = 60,
    level = 2,
    baseAttackPower = 4,
    baseDefense = 6,
    npcType = NPCType.FRIENDLY,
    experienceReward = 0,
    lootTable = emptyList(),
) {
    override fun getGreeting(player: Player): String {
        return "The scholar looks up from their research. 'Ah, a fellow seeker of knowledge! How fascinating to meet you here.'"
    }

    override fun handleDialogue(player: Player, message: String): String {
        return "The scholar nods thoughtfully. 'Indeed, there is much to learn in this region.'"
    }
}

package com.terheyden.mud.mudlib.rooms

import com.terheyden.mud.corelib.Direction
import com.terheyden.mud.corelib.puzzle.PuzzleRoom
import com.terheyden.mud.corelib.puzzle.PuzzleState
import com.terheyden.mud.corelib.room.RoomFeature
import com.terheyden.mud.mudlib.items.MirrorShard
import com.terheyden.mud.mudlib.items.ReflectionCloak
import org.springframework.stereotype.Component

/**
 * A maze of mirrors where players must navigate by following reflections and solving
 * visual puzzles to find the true path.
 */
@Component
class MirrorMaze : PuzzleRoom(
    id = "mirror_maze",
    name = "Mirror Maze",
    description = "You stand at the entrance of a bewildering maze constructed entirely of mirrors. " +
            "Countless reflections stretch in all directions, creating an infinite labyrinth of " +
            "images that shift and dance with every movement. The mirrors are of different types - " +
            "some show true reflections, others distort reality, and a few seem to show glimpses " +
            "of other places entirely. Soft, silvery light emanates from the mirrors themselves, " +
            "creating an ethereal, dreamlike atmosphere.",
    features = listOf(
        RoomFeature(
            id = "true_mirrors",
            names = listOf("true mirrors", "clear mirrors", "honest mirrors", "straight mirrors"),
            description = "These mirrors show perfect, undistorted reflections. They gleam with " +
                    "silver clarity and seem to be the most trustworthy guides through the maze. " +
                    "Looking into them, you see yourself exactly as you are, with no illusions " +
                    "or deceptions.",
            keywords = listOf(
                "perfect",
                "undistorted",
                "gleam",
                "silver",
                "clarity",
                "trustworthy",
                "guides",
                "exactly",
                "illusions",
                "deceptions"
            )
        ),
        RoomFeature(
            id = "distorting_mirrors",
            names = listOf("distorting mirrors", "warped mirrors", "twisted mirrors", "false mirrors"),
            description = "These mirrors bend and twist your reflection in impossible ways, making " +
                    "you appear tall and thin, short and wide, or fragmented into multiple pieces. " +
                    "They seem designed to confuse and mislead, their surfaces rippling like " +
                    "liquid mercury.",
            keywords = listOf(
                "bend",
                "twist",
                "impossible",
                "tall",
                "thin",
                "short",
                "wide",
                "fragmented",
                "multiple",
                "pieces",
                "confuse",
                "mislead",
                "rippling",
                "liquid",
                "mercury"
            )
        ),
        RoomFeature(
            id = "vision_mirrors",
            names = listOf("vision mirrors", "mystical mirrors", "scrying mirrors", "magical mirrors"),
            description = "These mirrors show not your reflection, but glimpses of other places " +
                    "and times. You see flashes of distant rooms, ancient events, and possible " +
                    "futures. The images shift constantly, like looking through windows into " +
                    "other worlds.",
            keywords = listOf(
                "glimpses",
                "places",
                "times",
                "flashes",
                "distant",
                "ancient",
                "events",
                "possible",
                "futures",
                "shift",
                "constantly",
                "windows",
                "worlds"
            )
        ),
        RoomFeature(
            id = "mirror_pattern",
            names = listOf("mirror pattern", "pattern", "arrangement", "maze pattern"),
            description = "Looking carefully at the arrangement of mirrors, you begin to notice " +
                    "a subtle pattern. The true mirrors seem to form a path, while the distorting " +
                    "mirrors create false routes. The vision mirrors might hold clues about the " +
                    "correct direction to take.",
            keywords = listOf(
                "carefully",
                "arrangement",
                "notice",
                "subtle",
                "form",
                "path",
                "false",
                "routes",
                "clues",
                "correct",
                "direction"
            )
        ),
        RoomFeature(
            id = "silvery_light",
            names = listOf("silvery light", "light", "ethereal light", "mirror light"),
            description = "The light in the maze comes from the mirrors themselves, creating a " +
                    "soft, silvery glow that seems to pulse gently. The light is strongest " +
                    "around the true mirrors and dimmer near the distorting ones.",
            keywords = listOf("comes", "soft", "glow", "pulse", "gently", "strongest", "dimmer")
        ),
    ),
    exits = mapOf(
        Direction.SOUTH to AlchemistLaboratory::class,
        Direction.EAST to ClockworkChamber::class,
    ),
    items = listOf(
        // Items will be revealed when puzzle is solved
    ),
) {

    private var currentPosition = 0
    private val correctPath = listOf("true", "true", "vision", "true", "vision", "true")
    private val playerPath = mutableListOf<String>()
    private var attempts = 0
    private val maxAttempts = 3

    override fun handlePuzzleInteraction(playerId: String, action: String, target: String): String? {
        return when (action.lowercase()) {
            "follow", "enter", "go", "move" -> handleMirrorNavigation(target)
            "examine", "look" -> handleMirrorExamination(target)
            "touch" -> handleMirrorTouch(target)
            else -> null
        }
    }

    private fun handleMirrorNavigation(target: String): String {
        if (puzzleState == PuzzleState.SOLVED) {
            return "You have already found the true path through the mirror maze. The exit is clearly visible."
        }

        val mirrorType = extractMirrorType(target)
        if (mirrorType == null) {
            return "You must specify which type of mirror to follow: 'true mirrors', 'distorting mirrors', or 'vision mirrors'."
        }

        playerPath.add(mirrorType)
        currentPosition++

        // Check if this step is correct
        if (currentPosition <= correctPath.size && mirrorType == correctPath[currentPosition - 1]) {
            return if (currentPosition >= correctPath.size) {
                // Puzzle complete!
                onPuzzleSolved("player")
                revealTreasures()
                "You follow the final true mirror and suddenly the maze opens up before you! " +
                        "The mirrors part like curtains, revealing a hidden chamber filled with " +
                        "treasures. You have successfully navigated the Mirror Maze!"
            } else {
                puzzleState = PuzzleState.IN_PROGRESS
                "You step toward the ${mirrorType} mirror and the maze shifts around you. " +
                        "The path continues deeper into the labyrinth. Progress: ${currentPosition}/${correctPath.size}"
            }
        } else {
            // Wrong choice
            attempts++
            return if (attempts >= maxAttempts) {
                onPuzzleFailed("player")
                resetMaze()
                "The mirrors flash angrily and you find yourself back at the entrance, completely " +
                        "lost and disoriented. The maze has reset itself. You must start over."
            } else {
                resetMaze()
                "The mirror you chose leads you in circles! The maze shifts and you find yourself " +
                        "back at the entrance. Attempts remaining: ${maxAttempts - attempts}. " +
                        "Study the mirrors more carefully before choosing your path."
            }
        }
    }

    private fun handleMirrorExamination(target: String): String? {
        val mirrorType = extractMirrorType(target)
        return when (mirrorType) {
            "true" -> "The true mirrors show your reflection clearly and honestly. They seem to " +
                    "form a path through the maze, glowing slightly brighter than the others."

            "distorting" -> "The distorting mirrors warp your reflection in bizarre ways. They " +
                    "seem designed to confuse and mislead travelers through the maze."

            "vision" -> "The vision mirrors show glimpses of other places. You see flashes of " +
                    "the chamber beyond the maze, hints of treasure, and the correct path forward."

            else -> {
                if (target.contains("pattern") || target.contains("maze")) {
                    return getPatternHint()
                }
                null
            }
        }
    }

    private fun handleMirrorTouch(target: String): String? {
        val mirrorType = extractMirrorType(target)
        return when (mirrorType) {
            "true" -> "The true mirror feels solid and warm to the touch, like polished silver."
            "distorting" -> "The distorting mirror feels strange and fluid, like touching liquid mercury."
            "vision" -> "The vision mirror tingles with magical energy when you touch it."
            else -> null
        }
    }

    private fun extractMirrorType(target: String): String? {
        val normalized = target.lowercase()
        return when {
            normalized.contains("true") || normalized.contains("clear") || normalized.contains("honest") -> "true"
            normalized.contains("distort") || normalized.contains("warp") || normalized.contains("false") -> "distorting"
            normalized.contains("vision") || normalized.contains("mystical") || normalized.contains("scrying") -> "vision"
            else -> null
        }
    }

    private fun getPatternHint(): String {
        return when (currentPosition) {
            0 -> "The pattern suggests starting with mirrors that show truth and clarity."
            1 -> "Continue following the path of honesty and truth."
            2 -> "Look for visions that might guide your next step."
            3 -> "Return to the path of truth after gaining insight."
            4 -> "Seek another vision to confirm your direction."
            5 -> "The final step should be one of truth and clarity."
            else -> "The pattern is complete - you have found the way."
        }
    }

    private fun resetMaze() {
        currentPosition = 0
        playerPath.clear()
    }

    private fun revealTreasures() {
        addItem(MirrorShard())
        addItem(ReflectionCloak())
    }

    override fun getSolvedDescription(): String {
        return "The mirror maze has parted to reveal its secrets. The mirrors now show only " +
                "true reflections, and a clear path leads to a treasure chamber filled with " +
                "mystical artifacts."
    }

    override fun resetPuzzle() {
        super.resetPuzzle()
        resetMaze()
        attempts = 0
        removeItem("mirror_shard")
        removeItem("reflection_cloak")
    }

    override fun onPlayerEnter(playerId: String) {
        if (puzzleState == PuzzleState.UNSOLVED) {
            // Could provide initial guidance about studying the mirrors
        }
    }
}

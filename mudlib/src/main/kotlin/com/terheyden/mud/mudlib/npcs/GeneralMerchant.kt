package com.terheyden.mud.mudlib.npcs

import com.terheyden.mud.corelib.living.NPC
import com.terheyden.mud.corelib.living.NPCType
import com.terheyden.mud.corelib.living.Player
import com.terheyden.mud.mudlib.items.BreadLoaf
import com.terheyden.mud.mudlib.items.GoldCoins
import com.terheyden.mud.mudlib.items.HealingPotion
import com.terheyden.mud.mudlib.items.Rope
import com.terheyden.mud.mudlib.items.Torch
import com.terheyden.mud.mudlib.items.TravelPack
import com.terheyden.mud.mudlib.items.WaterBottle

/**
 * A general merchant who sells various useful items and supplies.
 */
class GeneralMerchant : NPC(
    id = "general_merchant",
    name = "general merchant",
    description = "A middle-aged merchant with keen eyes and a friendly demeanor. He wears practical " +
            "clothes suitable for travel and trade, with a leather apron full of pockets for " +
            "coins and small items. His hands are stained with ink from keeping detailed " +
            "records of his transactions. He has the look of someone who knows the value " +
            "of everything and can find what you need.",
    inventory = listOf(
        HealingPotion(), HealingPotion(), HealingPotion(),
        BreadLoaf(), BreadLoaf(),
        GoldCoins(), GoldCoins(),
        Rope(), Rope(),
        Torch(), Torch(), Torch(),
        TravelPack(),
        WaterBottle(), WaterBottle(),
    ),
    maxHealthPoints = 90,
    currentHealthPoints = 90,
    level = 3,
    baseAttackPower = 5,
    baseDefense = 8,
    npcType = NPCType.FRIENDLY,
    experienceReward = 0,
    lootTable = emptyList(),
) {

    private var hasIntroduced = false
    private var tradeCount = 0

    override fun getGreeting(player: Player): String {
        return if (!hasIntroduced) {
            hasIntroduced = true
            "The merchant looks up from his ledger and smiles warmly. 'Welcome to my stall, " +
                    "traveler! I'm Marcus, and I've got just about everything an adventurer " +
                    "might need. Healing potions, food, rope, torches - you name it, I " +
                    "probably have it. What can I help you find today?'"
        } else {
            "Marcus grins and rubs his hands together. 'Back again? Excellent! What can " +
                    "I interest you in this time?'"
        }
    }

    override fun handleDialogue(player: Player, message: String): String {
        val lowerMessage = message.lowercase()

        return when {
            lowerMessage.contains("hello") || lowerMessage.contains("greetings") -> {
                getGreeting(player)
            }

            lowerMessage.contains("buy") || lowerMessage.contains("purchase") || lowerMessage.contains("trade") -> {
                tradeCount++
                when {
                    lowerMessage.contains("potion") || lowerMessage.contains("healing") -> {
                        "Marcus holds up a glowing red vial. 'Ah, healing potions! Essential for " +
                                "any adventurer. These are made by the local alchemist with the " +
                                "finest ingredients. They'll mend wounds and restore your vitality " +
                                "in no time. Twenty gold pieces each.'"
                    }

                    lowerMessage.contains("food") || lowerMessage.contains("bread") -> {
                        "Marcus gestures to fresh loaves on his counter. 'Fresh bread from the " +
                                "village bakery! Still warm and perfect for long journeys. " +
                                "Nothing beats good bread when you're on the road. Five gold pieces " +
                                "per loaf.'"
                    }

                    lowerMessage.contains("rope") -> {
                        "Marcus shows you coils of sturdy rope. 'Good hemp rope, strong enough " +
                                "to hold a horse! You never know when you'll need to climb down " +
                                "a cliff or tie up a prisoner. Ten gold pieces for fifty feet.'"
                    }

                    lowerMessage.contains("torch") || lowerMessage.contains("light") -> {
                        "Marcus picks up a well-made torch. 'These torches burn bright and long. " +
                                "Made with the best oil and wrapped tight. Essential for exploring " +
                                "dark places like caves or dungeons. Three gold pieces each.'"
                    }

                    lowerMessage.contains("pack") || lowerMessage.contains("bag") -> {
                        "Marcus shows you a well-crafted travel pack. 'This beauty will carry " +
                                "all your gear and then some. Made by the local leatherworker " +
                                "with reinforced stitching. It'll last you years. Thirty gold pieces.'"
                    }

                    lowerMessage.contains("water") || lowerMessage.contains("bottle") -> {
                        "Marcus holds up a leather water bottle. 'Clean water is life, friend. " +
                                "These bottles are waterproof and hold enough for a day's travel. " +
                                "Eight gold pieces each.'"
                    }

                    else -> {
                        "Marcus spreads his arms wide. 'I've got healing potions, fresh bread, " +
                                "sturdy rope, bright torches, travel packs, and water bottles. " +
                                "What specifically interests you? Just tell me what you need!'"
                    }
                }
            }

            lowerMessage.contains("sell") -> {
                "Marcus examines you appraisingly. 'I might be interested in buying certain " +
                        "items, depending on what you've got. Gems, rare materials, magical " +
                        "items - those sorts of things. What are you looking to sell?'"
            }

            lowerMessage.contains("price") || lowerMessage.contains("cost") || lowerMessage.contains("gold") -> {
                "Marcus counts on his fingers. 'Let's see... healing potions are twenty gold, " +
                        "bread is five gold, rope is ten gold, torches are three gold each, " +
                        "travel packs are thirty gold, and water bottles are eight gold. " +
                        "Fair prices for quality goods!'"
            }

            lowerMessage.contains("news") || lowerMessage.contains("information") -> {
                when (tradeCount % 3) {
                    0 -> "Marcus leans in conspiratorially. 'I've heard strange tales from " +
                            "travelers lately. Some speak of ancient ruins awakening, others " +
                            "of magical energies stirring in the old places. Exciting times " +
                            "for adventurers, I'd say!'"

                    1 -> "Marcus nods thoughtfully. 'The village has been busier than usual. " +
                            "More adventurers passing through, more demand for supplies. " +
                            "Something big is happening in the world, mark my words.'"

                    else -> "Marcus glances around and lowers his voice. 'Between you and me, " +
                            "I've been getting requests for some unusual items lately. Rare " +
                            "components, magical focuses, that sort of thing. The times are " +
                            "changing, friend.'"
                }
            }

            lowerMessage.contains("village") || lowerMessage.contains("town") -> {
                "Marcus gestures around the market. 'This is a good village, friend. Honest " +
                        "folk, fair prices, and always something interesting happening. The " +
                        "blacksmith makes fine weapons, the temple offers healing, and the " +
                        "inn serves the best ale for miles around.'"
            }

            lowerMessage.contains("goodbye") || lowerMessage.contains("farewell") -> {
                "Marcus waves cheerfully. 'Safe travels, friend! Come back anytime you need " +
                        "supplies. And remember - if you find anything interesting in your " +
                        "adventures, I might be interested in buying it!'"
            }

            else -> {
                val responses = listOf(
                    "Marcus nods attentively. 'Tell me more about that.'",
                    "Marcus strokes his chin thoughtfully. 'Interesting...'",
                    "Marcus's eyes light up with merchant's curiosity. 'Go on!'",
                    "Marcus leans forward with interest. 'That sounds profitable!'",
                    "Marcus chuckles and adjusts his apron. 'You adventurers always have the best stories.'"
                )
                responses.random()
            }
        }
    }
}

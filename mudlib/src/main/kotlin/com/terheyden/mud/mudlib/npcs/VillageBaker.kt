package com.terheyden.mud.mudlib.npcs

import com.terheyden.mud.corelib.living.NPC
import com.terheyden.mud.corelib.living.NPCType
import com.terheyden.mud.corelib.living.Player
import com.terheyden.mud.mudlib.items.BreadLoaf
import com.terheyden.mud.mudlib.items.Flour
import com.terheyden.mud.mudlib.items.GoldCoins
import com.terheyden.mud.mudlib.items.MeatPie
import com.terheyden.mud.mudlib.items.SweetRoll

/**
 * A cheerful village baker who creates delicious bread and pastries.
 */
class VillageBaker : NPC(
    id = "village_baker",
    name = "village baker",
    description = "A plump, cheerful woman with flour-dusted hands and a warm smile. Her apron " +
            "is stained with various ingredients, and her hair is tied back with a colorful " +
            "kerchief. She moves with the practiced efficiency of someone who has been baking " +
            "for decades, and the delicious aromas that surround her are testament to her " +
            "skill. Her eyes twinkle with the joy of someone who loves their work.",
    inventory = listOf(
        BreadLoaf(), <PERSON><PERSON><PERSON>oa<PERSON>(), <PERSON><PERSON><PERSON><PERSON><PERSON>(),
        <PERSON><PERSON><PERSON>(), <PERSON><PERSON><PERSON>(),
        <PERSON><PERSON><PERSON>(), <PERSON><PERSON><PERSON>(),
        <PERSON>lour(), <PERSON>lour(),
        <PERSON><PERSON><PERSON><PERSON>()
    ),
    maxHealthPoints = 85,
    currentHealthPoints = 85,
    level = 2,
    baseAttackPower = 6,
    baseDefense = 8,
    npcType = NPCType.FRIENDLY,
    experienceReward = 0,
    lootTable = emptyList(),
) {

    private var hasIntroduced = false
    private var recipesShared = 0

    override fun getGreeting(player: Player): String {
        return if (!hasIntroduced) {
            hasIntroduced = true
            "The baker looks up from kneading dough, her face lighting up with a warm smile. " +
                    "'Welcome to my bakery, dearie! I'm Martha, and I've been baking for this " +
                    "village for over thirty years. The smell of fresh bread is the best " +
                    "greeting I can offer! What can I tempt you with today?'"
        } else {
            "Martha wipes her floury hands on her apron and beams at you. 'Back for more " +
                    "of my baking? I'm so pleased! What catches your fancy today?'"
        }
    }

    override fun handleDialogue(player: Player, message: String): String {
        val lowerMessage = message.lowercase()

        return when {
            lowerMessage.contains("hello") || lowerMessage.contains("greetings") -> {
                getGreeting(player)
            }

            lowerMessage.contains("bread") || lowerMessage.contains("loaf") -> {
                "Martha proudly gestures to her fresh loaves. 'Ah, my bread! Made with the " +
                        "finest flour and baked to golden perfection. I start the dough before " +
                        "dawn so it's ready when the village wakes up. Nothing beats fresh bread " +
                        "for breakfast, lunch, or dinner! Five gold pieces per loaf.'"
            }

            lowerMessage.contains("sweet") || lowerMessage.contains("roll") || lowerMessage.contains("pastry") -> {
                "Martha's eyes light up as she shows you her sweet rolls. 'These little " +
                        "beauties are made with butter, sugar, and a touch of cinnamon. Perfect " +
                        "with morning tea or as an afternoon treat. The children in the village " +
                        "absolutely love them! Three gold pieces each.'"
            }

            lowerMessage.contains("pie") || lowerMessage.contains("meat") -> {
                "Martha lifts a golden-brown meat pie from the display. 'These hearty pies " +
                        "are filled with tender beef, vegetables, and rich gravy, all wrapped " +
                        "in my flaky pastry crust. Perfect for a filling meal, especially for " +
                        "hardworking folk like yourself. Eight gold pieces each.'"
            }

            lowerMessage.contains("recipe") || lowerMessage.contains("cook") || lowerMessage.contains("bake") -> {
                recipesShared++
                when (recipesShared % 3) {
                    1 -> "Martha leans in conspiratorially. 'The secret to good bread is patience, " +
                            "dearie. Let the dough rise slowly, don't rush it. And always use " +
                            "the best flour you can afford - it makes all the difference in " +
                            "the world!'"

                    2 -> "Martha winks and taps her nose. 'For sweet rolls, the trick is to " +
                            "add just a pinch of salt to bring out the sweetness. And never " +
                            "overwork the dough - gentle hands make tender pastries!'"

                    else -> "Martha chuckles warmly. 'Meat pies need a hot oven and a steady " +
                            "hand. The filling should be thick enough to hold together but " +
                            "not so thick it's dry. And always brush the crust with egg " +
                            "for that beautiful golden color!'"
                }
            }

            lowerMessage.contains("flour") || lowerMessage.contains("ingredients") -> {
                "Martha gestures to her flour sacks. 'I get my flour from the best mills " +
                        "in the region. Different grains for different purposes - wheat for " +
                        "bread, rye for hearty loaves, and oat flour for special occasions. " +
                        "Good ingredients are the foundation of good baking!'"
            }

            lowerMessage.contains("oven") || lowerMessage.contains("fire") -> {
                "Martha pats her stone oven affectionately. 'This old beauty has been baking " +
                        "bread for longer than I've been alive. The stone holds heat perfectly, " +
                        "and I know every hot spot and cool corner. We've been partners for " +
                        "thirty years now!'"
            }

            lowerMessage.contains("village") || lowerMessage.contains("people") -> {
                "Martha's face glows with affection. 'This village is my family, dearie. " +
                        "I've watched children grow up on my bread, seen couples share my " +
                        "wedding cakes, and comforted families with warm loaves in hard times. " +
                        "There's no place I'd rather be.'"
            }

            lowerMessage.contains("apprentice") || lowerMessage.contains("learn") -> {
                "Martha glances fondly at her apprentice. 'Young Sarah there is learning " +
                        "the trade. She's got good hands and a patient heart - essential " +
                        "qualities for a baker. I'm teaching her everything I know, just " +
                        "as my grandmother taught me.'"
            }

            lowerMessage.contains("price") || lowerMessage.contains("cost") || lowerMessage.contains("gold") -> {
                "Martha counts on her floury fingers. 'Let's see... bread loaves are five " +
                        "gold, sweet rolls are three gold, and meat pies are eight gold. " +
                        "Everything's made fresh daily with the finest ingredients!'"
            }

            lowerMessage.contains("special") || lowerMessage.contains("order") -> {
                "Martha's eyes brighten with interest. 'Special orders? Oh, I love a challenge! " +
                        "Wedding cakes, celebration breads, holiday pastries - just tell me " +
                        "what you need and when you need it. I'll make something wonderful " +
                        "for you!'"
            }

            lowerMessage.contains("morning") || lowerMessage.contains("early") -> {
                "Martha chuckles and stretches her back. 'I'm up before the roosters, dearie! " +
                        "Start the fires at four in the morning so the bread is ready when " +
                        "the village wakes up. It's hard work, but seeing people's faces " +
                        "when they smell fresh bread makes it all worthwhile.'"
            }

            lowerMessage.contains("goodbye") || lowerMessage.contains("farewell") -> {
                "Martha waves with a flour-dusted hand. 'Come back anytime, dearie! And " +
                        "remember - life's too short for stale bread. Always choose fresh!'"
            }

            else -> {
                val responses = listOf(
                    "Martha nods while kneading dough. 'Oh my, tell me more about that!'",
                    "Martha's eyes twinkle with interest. 'How fascinating!'",
                    "Martha pauses in her work to listen attentively. 'Go on, dearie.'",
                    "Martha smiles warmly while dusting flour from her hands. 'That sounds wonderful!'",
                    "Martha chuckles, a sound as warm as her ovens. 'You adventurers have such stories!'"
                )
                responses.random()
            }
        }
    }
}

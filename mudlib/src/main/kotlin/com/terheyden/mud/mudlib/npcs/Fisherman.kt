package com.terheyden.mud.mudlib.npcs

import com.terheyden.mud.corelib.living.NPC
import com.terheyden.mud.corelib.living.NPCType
import com.terheyden.mud.corelib.living.Player
import com.terheyden.mud.mudlib.items.FishingHook
import com.terheyden.mud.mudlib.items.FishingNet
import com.terheyden.mud.mudlib.items.FreshFish
import com.terheyden.mud.mudlib.items.GoldCoins

/**
 * An experienced fisherman who knows the waters and can share fishing wisdom.
 */
class Fisherman : NPC(
    id = "fisherman",
    name = "fisherman",
    description = "A weathered man with sun-darkened skin and calloused hands that speak of " +
            "years spent working with nets and lines. He wears simple, practical clothes " +
            "that smell faintly of fish and river water. His eyes are the color of deep " +
            "water, and they hold the patience that comes from long hours waiting for " +
            "the fish to bite. A wide-brimmed hat protects him from the sun.",
    inventory = listOf(
        FreshFish(), FreshFish(), FreshFish(),
        FishingNet(),
        FishingHook(), FishingHook(),
        GoldCoins()
    ),
    maxHealthPoints = 95,
    currentHealthPoints = 95,
    level = 3,
    baseAttackPower = 8,
    baseDefense = 10,
    npcType = NPCType.FRIENDLY,
    experienceReward = 0,
    lootTable = emptyList(),
) {

    private var fishingTipsGiven = 0

    override fun getGreeting(player: Player): String {
        return "The fisherman looks up from mending his net and nods in greeting. 'Afternoon, " +
                "friend. Name's Old <PERSON>, been fishing these waters for nigh on forty years. " +
                "The fish are running well today - caught a nice mess of them this morning. " +
                "You look like you might be interested in some fresh fish, or perhaps some " +
                "fishing wisdom?'"
    }

    override fun handleDialogue(player: Player, message: String): String {
        val lowerMessage = message.lowercase()

        return when {
            lowerMessage.contains("hello") || lowerMessage.contains("greetings") -> {
                getGreeting(player)
            }

            lowerMessage.contains("fish") && (lowerMessage.contains("buy") || lowerMessage.contains("fresh")) -> {
                "Old Pete gestures to his catch of the day. 'Fresh as they come, friend! " +
                        "Caught them this very morning when the mist was still on the water. " +
                        "Nothing beats fresh fish for a good meal - full of flavor and " +
                        "good for your health too. Three gold pieces each.'"
            }

            lowerMessage.contains("fishing") || lowerMessage.contains("catch") -> {
                fishingTipsGiven++
                when (fishingTipsGiven % 4) {
                    1 -> "Old Pete strokes his beard thoughtfully. 'The secret to good fishing " +
                            "is patience, friend. The fish can sense if you're in a hurry. " +
                            "Sit quiet, keep still, and let the river do the work. The fish " +
                            "will come when they're ready.'"

                    2 -> "Old Pete points to the water. 'Early morning and late evening are " +
                            "the best times. That's when the fish are feeding. And watch the " +
                            "water - if you see ripples or jumping fish, that's where you " +
                            "want to cast your line.'"

                    3 -> "Old Pete holds up a well-used hook. 'Use the right bait for the " +
                            "fish you're after. Worms for most river fish, but some prefer " +
                            "insects or small bits of bread. And keep your hooks sharp - " +
                            "a dull hook won't set properly.'"

                    else -> "Old Pete nods sagely. 'Respect the water and it'll provide for " +
                            "you. Don't take more than you need, and always throw back the " +
                            "small ones so they can grow and breed. The river's been good " +
                            "to me, so I'm good to it.'"
                }
            }

            lowerMessage.contains("net") || lowerMessage.contains("equipment") -> {
                "Old Pete shows you his fishing net. 'This net's been with me for twenty " +
                        "years. Made it myself from the finest cord I could afford. A good " +
                        "net is worth its weight in gold to a fisherman - it needs to be " +
                        "strong but not so heavy it sinks too fast.'"
            }

            lowerMessage.contains("river") || lowerMessage.contains("water") -> {
                "Old Pete gazes out at the flowing water. 'This river's been my life, friend. " +
                        "I know every bend, every deep hole, every place the fish like to " +
                        "hide. The water's clean and the fish are healthy - we're blessed " +
                        "to have such a fine river running past our village.'"
            }

            lowerMessage.contains("village") || lowerMessage.contains("people") -> {
                "Old Pete smiles warmly. 'Good folk in this village. They appreciate fresh " +
                        "fish, and I appreciate their business. Been selling my catch here " +
                        "for decades. The baker sometimes trades bread for fish, and the " +
                        "innkeeper always needs fish for his stew.'"
            }

            lowerMessage.contains("weather") || lowerMessage.contains("storm") -> {
                "Old Pete squints at the sky. 'You learn to read the weather when you spend " +
                        "your life on the water. Fish bite better before a storm - something " +
                        "about the pressure change gets them active. But you don't want to " +
                        "be caught on the water when the lightning starts!'"
            }

            lowerMessage.contains("boat") || lowerMessage.contains("dock") -> {
                "Old Pete gestures to his small fishing boat. 'She's not much to look at, " +
                        "but she's seaworthy and knows these waters as well as I do. Built " +
                        "her myself with help from the village carpenter. A fisherman's boat " +
                        "is his most important tool.'"
            }

            lowerMessage.contains("learn") || lowerMessage.contains("teach") -> {
                "Old Pete's eyes light up. 'Always happy to teach someone who's genuinely " +
                        "interested! Fishing's not just about catching fish - it's about " +
                        "understanding the water, the weather, and the fish themselves. " +
                        "Come back anytime and I'll share what I know.'"
            }

            lowerMessage.contains("goodbye") || lowerMessage.contains("farewell") -> {
                "Old Pete tips his hat. 'Fair winds and tight lines, friend! May your " +
                        "nets always be full and your hooks always sharp. Come back anytime " +
                        "you want fresh fish or fishing talk!'"
            }

            else -> {
                val responses = listOf(
                    "Old Pete nods thoughtfully while working on his net. 'Aye, that's interesting.'",
                    "Old Pete pauses in his work to listen. 'Tell me more about that.'",
                    "Old Pete's weathered face shows interest. 'Go on, friend.'",
                    "Old Pete chuckles, a sound like water over stones. 'You've got some tales!'",
                    "Old Pete adjusts his hat and gives you his full attention. 'I'm listening.'"
                )
                responses.random()
            }
        }
    }
}

package com.terheyden.mud.mudlib.rooms

import com.terheyden.mud.corelib.Direction
import com.terheyden.mud.corelib.room.Room
import com.terheyden.mud.corelib.room.RoomFeature
import com.terheyden.mud.mudlib.items.MapOfRegion
import com.terheyden.mud.mudlib.items.SkillBook
import com.terheyden.mud.mudlib.items.VillageHistory
import com.terheyden.mud.mudlib.npcs.ScholarlyVisitor
import com.terheyden.mud.mudlib.npcs.VillageLibrarian
import org.springframework.stereotype.Component

/**
 * A cozy village library filled with books, maps, and local knowledge.
 */
@Component
class VillageLibrary : Room(
    id = "village_library",
    name = "Village Library",
    description = "You enter a cozy library filled with the comforting smell of old books and " +
            "parchment. Tall wooden shelves line the walls, packed with volumes on history, " +
            "geography, crafts, and local lore. Reading tables with comfortable chairs are " +
            "scattered throughout the room, each with its own oil lamp for good light. " +
            "Maps and charts hang on the walls, showing the local region and distant lands. " +
            "This is clearly a place of learning and quiet contemplation.",
    features = listOf(
        RoomFeature(
            id = "book_shelves",
            names = listOf("book shelves", "shelves", "wooden shelves", "tall shelves"),
            description = "Tall wooden shelves stretch from floor to ceiling, filled with books " +
                    "of all sizes and subjects. The books are well-organized by topic, with " +
                    "small labels indicating sections for history, geography, crafts, magic, " +
                    "and local lore. Many of the books show signs of frequent use, their " +
                    "spines worn smooth by countless hands.",
            keywords = listOf(
                "tall",
                "wooden",
                "stretch",
                "floor",
                "ceiling",
                "filled",
                "books",
                "sizes",
                "subjects",
                "well-organized",
                "topic",
                "small",
                "labels",
                "sections",
                "history",
                "geography",
                "crafts",
                "magic",
                "local",
                "lore",
                "frequent",
                "use",
                "spines",
                "worn",
                "smooth",
                "countless",
                "hands"
            )
        ),
        RoomFeature(
            id = "reading_tables",
            names = listOf("reading tables", "tables", "study tables", "wooden tables"),
            description = "Several sturdy wooden tables provide comfortable spaces for reading " +
                    "and study. Each table has a cushioned chair and its own oil lamp with " +
                    "a polished reflector to provide good light. Inkwells, quills, and " +
                    "blank parchment are available for those who wish to take notes.",
            keywords = listOf(
                "sturdy",
                "wooden",
                "comfortable",
                "spaces",
                "reading",
                "study",
                "cushioned",
                "chair",
                "oil",
                "lamp",
                "polished",
                "reflector",
                "good",
                "light",
                "inkwells",
                "quills",
                "blank",
                "parchment",
                "available",
                "notes"
            )
        ),
        RoomFeature(
            id = "wall_maps",
            names = listOf("wall maps", "maps", "charts", "regional maps"),
            description = "Large maps and charts hang on the walls, showing the local region " +
                    "in great detail. You can see the village, surrounding forests, rivers, " +
                    "mountains, and other settlements. Some maps show trade routes, while " +
                    "others mark areas of historical significance or rumored danger.",
            keywords = listOf(
                "large",
                "maps",
                "charts",
                "hang",
                "walls",
                "local",
                "region",
                "great",
                "detail",
                "village",
                "surrounding",
                "forests",
                "rivers",
                "mountains",
                "settlements",
                "trade",
                "routes",
                "historical",
                "significance",
                "rumored",
                "danger"
            )
        ),
        RoomFeature(
            id = "local_history_section",
            names = listOf("local history section", "history section", "history books", "local lore"),
            description = "A special section is dedicated to local history and lore. These books " +
                    "contain stories of the village's founding, tales of local heroes, records " +
                    "of important events, and folklore passed down through generations. The " +
                    "collection includes both formal histories and collections of folk tales.",
            keywords = listOf(
                "special",
                "section",
                "dedicated",
                "local",
                "history",
                "lore",
                "books",
                "contain",
                "stories",
                "founding",
                "tales",
                "heroes",
                "records",
                "important",
                "events",
                "folklore",
                "passed",
                "generations",
                "collection",
                "formal",
                "histories",
                "folk",
                "tales"
            )
        ),
        RoomFeature(
            id = "skill_manuals",
            names = listOf("skill manuals", "manuals", "instruction books", "how-to books"),
            description = "A collection of practical manuals and instruction books covers various " +
                    "skills and crafts. You can find guides to blacksmithing, farming, cooking, " +
                    "basic magic, herbalism, and many other useful subjects. These books are " +
                    "clearly popular with villagers looking to learn new skills.",
            keywords = listOf(
                "collection",
                "practical",
                "manuals",
                "instruction",
                "books",
                "covers",
                "various",
                "skills",
                "crafts",
                "guides",
                "blacksmithing",
                "farming",
                "cooking",
                "basic",
                "magic",
                "herbalism",
                "useful",
                "subjects",
                "popular",
                "villagers",
                "learn",
                "new",
                "skills"
            )
        ),
        RoomFeature(
            id = "librarian_desk",
            names = listOf("librarian desk", "desk", "wooden desk", "library desk"),
            description = "A large wooden desk serves as the librarian's workspace. It's covered " +
                    "with catalogues, lending records, and correspondence with other libraries " +
                    "and scholars. A comfortable chair sits behind it, and the desk drawers " +
                    "are filled with supplies for maintaining and repairing books.",
            keywords = listOf(
                "large",
                "wooden",
                "desk",
                "librarian",
                "workspace",
                "covered",
                "catalogues",
                "lending",
                "records",
                "correspondence",
                "libraries",
                "scholars",
                "comfortable",
                "chair",
                "behind",
                "drawers",
                "filled",
                "supplies",
                "maintaining",
                "repairing",
                "books"
            )
        ),
    ),
    exits = mapOf(
        Direction.NORTH to VillageTemple::class,
    ),
    items = listOf(
        VillageLibrarian(),
        ScholarlyVisitor(),
        VillageHistory(),
        MapOfRegion(),
        SkillBook(),
    ),
) {

    override fun onPlayerEnter(playerId: String) {
        // Could add book borrowing system, research quests, or skill learning
    }
}

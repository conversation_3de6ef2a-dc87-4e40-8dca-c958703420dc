package com.terheyden.mud.mudlib.rooms

import com.terheyden.mud.corelib.Direction
import com.terheyden.mud.corelib.room.Room
import com.terheyden.mud.corelib.room.RoomFeature
import com.terheyden.mud.mudlib.items.MagicalTome
import com.terheyden.mud.mudlib.items.TreasureChest
import org.springframework.stereotype.Component

/**
 * The basement beneath the ancient tower, filled with forgotten magical artifacts.
 */
@Component
class TowerBasement : Room(
    id = "tower_basement",
    name = "Tower Basement",
    description = "You stand in the basement of the ancient tower, a circular chamber with " +
            "vaulted stone ceilings. Dusty shelves line the walls, filled with forgotten tomes " +
            "and mysterious artifacts. A large wooden table dominates the center of the room, " +
            "covered with alchemical equipment and strange diagrams. The air smells of old " +
            "parchment, herbs, and something faintly magical.",
    features = listOf(
        RoomFeature(
            id = "dusty_shelves",
            names = listOf("dusty shelves", "shelves", "forgotten tomes", "tomes"),
            description = "The shelves are carved from the same stone as the tower walls and are " +
                    "filled with ancient books, scrolls, and mysterious objects. Most of the " +
                    "books are too damaged to read, but you can make out titles in various " +
                    "languages. Some objects seem to hum with residual magical energy.",
            keywords = listOf("carved", "ancient", "scrolls", "damaged", "languages", "hum", "residual", "energy")
        ),
        RoomFeature(
            id = "alchemical_table",
            names = listOf("wooden table", "table", "alchemical equipment", "equipment"),
            description = "The large oak table is covered with an array of alchemical equipment: " +
                    "glass vials, copper tubes, measuring scales, and a small cauldron. " +
                    "Strange diagrams are carved directly into the wood, showing complex " +
                    "magical formulas and ingredient combinations.",
            keywords = listOf(
                "oak",
                "vials",
                "copper",
                "tubes",
                "scales",
                "cauldron",
                "diagrams",
                "formulas",
                "ingredients"
            )
        ),
        RoomFeature(
            id = "stone_stairs",
            names = listOf("stone stairs", "stairs", "spiral stairs"),
            description = "A narrow spiral staircase winds upward, carved directly from the tower's " +
                    "stone foundation. The steps are worn smooth by countless feet over the " +
                    "centuries, and iron brackets hold unlit torches along the walls.",
            keywords = listOf("spiral", "foundation", "worn", "smooth", "centuries", "brackets", "unlit", "torches")
        ),
    ),
    exits = mapOf(
        Direction.UP to TowerEntrance::class,
        Direction.NORTH to UndergroundTunnel::class,
        Direction.WEST to AlchemistLaboratory::class,
    ),
    items = listOf(
        MagicalTome(),
        TreasureChest(),
    ),
) {

    override fun onPlayerEnter(playerId: String) {
        // The basement could have special magical effects
        // - Temporary magical knowledge buffs
        // - Chance to learn new abilities
        // - Magical item identification
    }
}

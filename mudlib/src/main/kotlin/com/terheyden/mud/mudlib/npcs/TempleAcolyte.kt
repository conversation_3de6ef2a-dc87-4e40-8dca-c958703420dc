package com.terheyden.mud.mudlib.npcs

import com.terheyden.mud.corelib.living.NPC
import com.terheyden.mud.corelib.living.NPCType
import com.terheyden.mud.corelib.living.Player
import com.terheyden.mud.mudlib.items.BlessedCandle
import com.terheyden.mud.mudlib.items.PrayerBook

class TempleAcolyte : NPC(
    id = "temple_acolyte",
    name = "temple acolyte",
    description = "A young acolyte learning the ways of healing and spiritual guidance.",
    inventory = listOf(BlessedCandle(), PrayerBook()),
    maxHealthPoints = 60,
    currentHealthPoints = 60,
    level = 1,
    baseAttackPower = 2,
    baseDefense = 8,
    npcType = NPCType.FRIENDLY,
    experienceReward = 0,
    lootTable = emptyList(),
) {
    override fun getGreeting(player: Player): String {
        return "The young acolyte bows respectfully. 'Peace be with you, traveler.'"
    }

    override fun handleDialogue(player: Player, message: String): String {
        return "The acolyte listens attentively. 'The priest can help you better than I.'"
    }
}

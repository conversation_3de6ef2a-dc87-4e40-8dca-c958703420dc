package com.terheyden.mud.mudlib.rooms

import com.terheyden.mud.corelib.Direction
import com.terheyden.mud.corelib.room.Room
import com.terheyden.mud.corelib.room.RoomFeature
import com.terheyden.mud.mudlib.items.ElementalCrystal
import com.terheyden.mud.mudlib.items.HealingPotion
import org.springframework.stereotype.Component

/**
 * A shrine dedicated to the elemental forces, where the power of the runic circle
 * can be channeled and focused.
 */
@Component
class ElementalShrine : Room(
    id = "elemental_shrine",
    name = "Elemental Shrine",
    description = "You enter a serene shrine dedicated to the elemental forces. Five pedestals " +
            "are arranged in a circle, each one dedicated to a different element. The air here " +
            "feels charged with elemental energy, and you can sense the connection to the runic " +
            "circle to the south. Soft light emanates from crystals embedded in the walls, " +
            "creating a peaceful, meditative atmosphere.",
    features = listOf(
        RoomFeature(
            id = "elemental_pedestals",
            names = listOf("elemental pedestals", "pedestals", "shrine pedestals"),
            description = "Five marble pedestals are arranged in a perfect circle, each carved " +
                    "with symbols representing earth, water, fire, air, and spirit. The pedestals " +
                    "glow softly with their respective elemental energies.",
            keywords = listOf(
                "marble",
                "perfect",
                "circle",
                "carved",
                "symbols",
                "earth",
                "water",
                "fire",
                "air",
                "spirit",
                "glow",
                "softly",
                "respective",
                "energies"
            )
        ),
        RoomFeature(
            id = "wall_crystals",
            names = listOf("wall crystals", "crystals", "embedded crystals", "glowing crystals"),
            description = "Crystals of various colors are embedded in the shrine walls, each one " +
                    "attuned to a different elemental force. They provide gentle illumination " +
                    "and seem to resonate with the elemental energies in the room.",
            keywords = listOf(
                "various",
                "colors",
                "embedded",
                "attuned",
                "different",
                "force",
                "gentle",
                "illumination",
                "resonate"
            )
        ),
        RoomFeature(
            id = "meditation_circle",
            names = listOf("meditation circle", "circle", "sacred circle"),
            description = "A circular pattern is inlaid in the floor at the center of the shrine, " +
                    "made from different colored stones representing the elements. This appears " +
                    "to be a place for meditation and elemental communion.",
            keywords = listOf(
                "circular",
                "pattern",
                "inlaid",
                "floor",
                "center",
                "colored",
                "stones",
                "representing",
                "meditation",
                "communion"
            )
        ),
    ),
    exits = mapOf(
        Direction.SOUTH to RunicCircle::class,
    ),
    items = listOf(
        ElementalCrystal(),
        HealingPotion(),
    ),
) {

    override fun onPlayerEnter(playerId: String) {
        // The shrine could provide elemental healing or buffs
        // Could also serve as a place to recharge elemental items
    }
}

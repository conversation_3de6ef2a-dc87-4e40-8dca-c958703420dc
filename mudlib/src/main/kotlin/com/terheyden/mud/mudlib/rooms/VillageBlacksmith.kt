package com.terheyden.mud.mudlib.rooms

import com.terheyden.mud.corelib.Direction
import com.terheyden.mud.corelib.room.Room
import com.terheyden.mud.corelib.room.RoomFeature
import com.terheyden.mud.mudlib.items.IronIngot
import com.terheyden.mud.mudlib.items.IronSword
import com.terheyden.mud.mudlib.items.LeatherArmor
import com.terheyden.mud.mudlib.npcs.BlacksmithApprentice
import org.springframework.stereotype.Component
import com.terheyden.mud.mudlib.npcs.VillageBlacksmith as BlacksmithNPC

/**
 * A busy blacksmith shop where weapons and armor are forged and repaired.
 */
@Component
class VillageBlacksmith : Room(
    id = "village_blacksmith",
    name = "Village Blacksmith",
    description = "You enter a hot, smoky blacksmith shop filled with the rhythmic sound of " +
            "hammer on anvil. The air shimmers with heat from the roaring forge, and sparks " +
            "fly as the blacksmith works red-hot metal. Weapons and armor hang from the walls " +
            "and rest on wooden racks, displaying the smith's skill and craftsmanship. The " +
            "smell of hot metal, coal, and quenching oil fills the air.",
    features = listOf(
        RoomFeature(
            id = "roaring_forge",
            names = listOf("roaring forge", "forge", "fire", "furnace"),
            description = "A massive stone forge dominates one wall, its fire burning white-hot " +
                    "and casting dancing shadows throughout the shop. The forge is fed by a " +
                    "bellows system that keeps the flames at the perfect temperature for " +
                    "metalworking. The heat is almost overwhelming, but clearly necessary " +
                    "for the blacksmith's craft.",
            keywords = listOf(
                "massive",
                "stone",
                "dominates",
                "wall",
                "burning",
                "white-hot",
                "casting",
                "dancing",
                "shadows",
                "fed",
                "bellows",
                "system",
                "flames",
                "perfect",
                "temperature",
                "metalworking",
                "overwhelming",
                "necessary",
                "craft"
            )
        ),
        RoomFeature(
            id = "anvil_workstation",
            names = listOf("anvil workstation", "anvil", "workstation", "iron anvil"),
            description = "A heavy iron anvil sits on a thick wooden stump, its surface polished " +
                    "smooth by countless hours of hammering. Various hammers, tongs, and other " +
                    "smithing tools are arranged within easy reach. The anvil bears the scars " +
                    "of years of metalworking, each mark telling a story of creation.",
            keywords = listOf(
                "heavy",
                "iron",
                "thick",
                "wooden",
                "stump",
                "surface",
                "polished",
                "smooth",
                "countless",
                "hammering",
                "various",
                "hammers",
                "tongs",
                "smithing",
                "tools",
                "arranged",
                "easy",
                "reach",
                "bears",
                "scars",
                "years",
                "metalworking",
                "mark",
                "story",
                "creation"
            )
        ),
        RoomFeature(
            id = "weapon_displays",
            names = listOf("weapon displays", "weapons", "weapon racks", "displayed weapons"),
            description = "Swords, axes, maces, and spears hang from wall-mounted racks and stands. " +
                    "Each weapon gleams with careful maintenance and shows the blacksmith's " +
                    "expertise in both form and function. Some are clearly for sale, while " +
                    "others appear to be masterwork examples of the craft.",
            keywords = listOf(
                "swords",
                "axes",
                "maces",
                "spears",
                "hang",
                "wall-mounted",
                "racks",
                "stands",
                "gleams",
                "careful",
                "maintenance",
                "shows",
                "expertise",
                "form",
                "function",
                "sale",
                "masterwork",
                "examples",
                "craft"
            )
        ),
        RoomFeature(
            id = "armor_stands",
            names = listOf("armor stands", "armor", "displayed armor", "suits of armor"),
            description = "Several wooden mannequins display suits of armor - leather, chainmail, " +
                    "and plate armor in various stages of completion. The armor shows excellent " +
                    "craftsmanship, with careful attention to both protection and mobility. " +
                    "Some pieces bear decorative elements that elevate them beyond mere function.",
            keywords = listOf(
                "wooden",
                "mannequins",
                "suits",
                "leather",
                "chainmail",
                "plate",
                "various",
                "stages",
                "completion",
                "excellent",
                "craftsmanship",
                "careful",
                "attention",
                "protection",
                "mobility",
                "pieces",
                "bear",
                "decorative",
                "elements",
                "elevate",
                "mere",
                "function"
            )
        ),
        RoomFeature(
            id = "quenching_barrel",
            names = listOf("quenching barrel", "barrel", "water barrel", "oil barrel"),
            description = "A large wooden barrel filled with oil stands ready for quenching hot " +
                    "metal. Steam occasionally rises from its surface when hot steel is plunged " +
                    "into it. The oil has a distinctive smell and is clearly an important part " +
                    "of the tempering process.",
            keywords = listOf(
                "large",
                "wooden",
                "filled",
                "oil",
                "stands",
                "ready",
                "quenching",
                "hot",
                "metal",
                "steam",
                "occasionally",
                "rises",
                "surface",
                "steel",
                "plunged",
                "distinctive",
                "smell",
                "important",
                "part",
                "tempering",
                "process"
            )
        ),
        RoomFeature(
            id = "tool_collection",
            names = listOf("tool collection", "tools", "smithing tools", "blacksmith tools"),
            description = "An impressive collection of specialized tools hangs from hooks and " +
                    "rests in wooden holders - hammers of various sizes, tongs, files, " +
                    "punches, and other implements essential to the blacksmith's trade. " +
                    "Each tool shows signs of heavy use and careful maintenance.",
            keywords = listOf(
                "impressive",
                "collection",
                "specialized",
                "tools",
                "hangs",
                "hooks",
                "rests",
                "wooden",
                "holders",
                "hammers",
                "various",
                "sizes",
                "tongs",
                "files",
                "punches",
                "implements",
                "essential",
                "trade",
                "signs",
                "heavy",
                "use",
                "careful",
                "maintenance"
            )
        ),
    ),
    exits = mapOf(
        Direction.SOUTH to VillageMarket::class,
        Direction.EAST to VillageGuardPost::class,
    ),
    items = listOf(
        BlacksmithNPC(),
        BlacksmithApprentice(),
        IronSword(),
        LeatherArmor(),
        IronIngot(),
    ),
) {

    override fun onPlayerEnter(playerId: String) {
        // Could add weapon repair services, custom forging, or apprenticeship opportunities
    }
}

package com.terheyden.mud.mudlib.rooms

import com.terheyden.mud.corelib.Direction
import com.terheyden.mud.corelib.room.Room
import com.terheyden.mud.corelib.room.RoomFeature
import com.terheyden.mud.mudlib.items.FreshFish
import com.terheyden.mud.mudlib.items.GoldCoins
import com.terheyden.mud.mudlib.npcs.Farmer
import org.springframework.stereotype.Component

/**
 * The outskirts of the village where farms and countryside begin.
 */
@Component
class VillageOutskirts : Room(
    id = "village_outskirts",
    name = "Village Outskirts",
    description = "You stand at the edge of the village where the buildings give way to " +
            "farmland and countryside. Fields of crops stretch into the distance, and " +
            "farm buildings dot the landscape. The air is fresh and clean, filled with " +
            "the scents of growing things and rural life.",
    features = listOf(
        RoomFeature(
            id = "crop_fields",
            names = listOf("crop fields", "fields", "crops", "farmland"),
            description = "Well-tended fields stretch into the distance, growing various crops.",
            keywords = listOf("well-tended", "fields", "stretch", "distance", "various", "crops")
        ),
    ),
    exits = mapOf(
        Direction.SOUTH to VillageDocks::class,
        Direction.EAST to VillageGuardPost::class,
    ),
    items = listOf(
        Farmer(),
        FreshFish(),
        GoldCoins(),
    ),
)

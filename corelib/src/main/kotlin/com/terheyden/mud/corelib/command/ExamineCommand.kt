package com.terheyden.mud.corelib.command

import com.terheyden.mud.corelib.GameService
import org.springframework.stereotype.Component

/**
 * Command to examine items in detail.
 */
@Component
class ExamineCommand : Command {
    override val name = "examine"
    override val aliases = listOf("ex", "inspect")
    override val description = "Examine an item in detail"

    override fun execute(args: List<String>): CommandResult {
        if (args.isEmpty()) {
            return CommandResult("Examine what? Specify an item to examine.")
        }

        val player = GameService.player
        val currentRoom = player.currentRoom
        val targetName = args.joinToString(" ")

        // Check items first (room, then inventory)
        val item = currentRoom.findItem(targetName) ?: player.findInInventory(targetName)
        if (item != null) {
            return CommandResult(item.getExamineDescription())
        }

        // Check room features
        val feature = currentRoom.findFeature(targetName)
        if (feature != null) {
            return CommandResult(feature.description)
        }

        return CommandResult("There is no '$targetName' here or in your inventory to examine.")
    }
}

package com.terheyden.mud.corelib.puzzle

import com.terheyden.mud.corelib.Direction
import com.terheyden.mud.corelib.Item
import com.terheyden.mud.corelib.room.Door
import com.terheyden.mud.corelib.room.Room
import com.terheyden.mud.corelib.room.RoomFeature
import kotlin.reflect.KClass

/**
 * Base class for rooms that contain puzzles.
 * Provides common functionality for puzzle state management and interactions.
 */
abstract class PuzzleRoom(
    id: String,
    name: String,
    description: String,
    features: List<RoomFeature> = emptyList(),
    exits: Map<Direction, KClass<out Room>>,
    doors: List<Door> = emptyList(),
    items: List<Item> = emptyList(),
    override var puzzleState: PuzzleState = PuzzleState.UNSOLVED,
) : Room(
    id = id,
    name = name,
    description = description,
    features = features,
    exits = exits,
    doors = doors,
    items = items,
), PuzzleTracker {

    /**
     * Handle puzzle-specific interactions.
     * This method should be overridden by specific puzzle rooms.
     *
     * @param playerId The ID of the player attempting the interaction
     * @param action The action being attempted (e.g., "say", "use", "examine")
     * @param target The target of the action (e.g., item name, phrase)
     * @return A message describing the result of the interaction, or null if not handled
     */
    abstract fun handlePuzzleInteraction(playerId: String, action: String, target: String): String?

    /**
     * Get the description that should be shown when the puzzle is solved.
     */
    abstract fun getSolvedDescription(): String

    /**
     * Get additional description text based on the current puzzle state.
     */
    open fun getPuzzleStateDescription(): String {
        return when (puzzleState) {
            PuzzleState.UNSOLVED -> ""
            PuzzleState.IN_PROGRESS -> "\nSomething here seems to be responding to your actions..."
            PuzzleState.SOLVED -> "\n${getSolvedDescription()}"
            PuzzleState.FAILED -> "\nSomething went wrong here. Perhaps you could try again?"
        }
    }

    /**
     * Get the full room description including puzzle state.
     */
    fun getFullDescriptionWithPuzzle(showExaminableHint: Boolean = false): String {
        return super.getFullDescription(showExaminableHint) + getPuzzleStateDescription()
    }

    /**
     * Called when the puzzle is successfully solved.
     * Override to add custom behavior when puzzle completion occurs.
     */
    open fun onPuzzleSolved(playerId: String): String? {
        solvePuzzle()
        return "You hear a satisfying click as something unlocks or activates!"
    }

    /**
     * Called when the puzzle fails.
     * Override to add custom behavior when puzzle failure occurs.
     */
    open fun onPuzzleFailed(playerId: String): String? {
        puzzleState = PuzzleState.FAILED
        return "Something doesn't seem right. Perhaps you should try a different approach."
    }

    override fun resetPuzzle() {
        puzzleState = PuzzleState.UNSOLVED
    }
}

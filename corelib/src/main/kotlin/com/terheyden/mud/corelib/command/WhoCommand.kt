package com.terheyden.mud.corelib.command

import com.terheyden.mud.corelib.GameService
import org.springframework.stereotype.Component

/**
 * Command to see who is in the current room.
 */
@Component
class WhoCommand : Command {
    override val name = "who"
    override val aliases = listOf("people", "npcs", "characters")
    override val description = "See who is present in the current room"

    override fun execute(args: List<String>): CommandResult {
        val player = GameService.player
        val currentRoom = player.currentRoom
        val npcs = currentRoom.contentsNPCs

        return if (npcs.isEmpty()) {
            CommandResult("You are alone here.")
        } else {
            val result = StringBuilder()
            result.appendLine("Present in ${currentRoom.name}:")

            npcs.forEach { npc ->
                val status = when {
                    npc.isDead() -> " (dead)"
                    npc.currentHealthPoints < npc.maxHealthPoints * 0.3 -> " (badly wounded)"
                    npc.currentHealthPoints < npc.maxHealthPoints * 0.7 -> " (wounded)"
                    else -> ""
                }

                val attitude = when (npc.npcType) {
                    com.terheyden.mud.corelib.living.NPCType.FRIENDLY -> " (friendly)"
                    com.terheyden.mud.corelib.living.NPCType.AGGRESSIVE -> " (hostile)"
                    com.terheyden.mud.corelib.living.NPCType.NEUTRAL -> ""
                }

                result.appendLine("  - ${npc.name}$status$attitude")
            }

            CommandResult(result.toString())
        }
    }
}

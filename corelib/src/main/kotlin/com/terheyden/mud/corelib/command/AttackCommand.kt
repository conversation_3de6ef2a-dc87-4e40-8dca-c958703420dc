package com.terheyden.mud.corelib.command

import com.terheyden.mud.corelib.GameService
import com.terheyden.mud.corelib.living.NPC
import com.terheyden.mud.corelib.living.NPCType
import com.terheyden.mud.corelib.living.Player
import com.terheyden.mud.corelib.room.Room
import org.springframework.stereotype.Component
import kotlin.random.Random

/**
 * Command to attack NPCs in combat.
 */
@Component
class AttackCommand : Command {
    override val name = "attack"
    override val aliases = listOf("fight", "kill", "hit")
    override val description = "Attack an NPC or creature"

    override fun execute(args: List<String>): CommandResult {
        if (args.isEmpty()) {
            return CommandResult("Attack what? Specify a target to attack.")
        }

        val player = GameService.player
        val currentRoom = player.currentRoom
        val targetName = args.joinToString(" ")

        // Find NPCs in the current room
        val npcsInRoom = currentRoom.contentsNPCs
        val target = npcsInRoom.find { it.matches(targetName) }

        if (target == null) {
            return CommandResult("There is no '$targetName' here to attack.")
        }

        if (target.isDead()) {
            return CommandResult("The ${target.name} is already dead.")
        }

        if (player.isDead()) {
            return CommandResult("You are dead and cannot attack!")
        }

        // Check if attacking a friendly NPC
        if (target.npcType == NPCType.FRIENDLY) {
            return CommandResult("You cannot bring yourself to attack the friendly ${target.name}!")
        }

        // Perform combat round
        return performCombatRound(player, target, currentRoom)
    }

    private fun performCombatRound(player: Player, target: NPC, room: Room): CommandResult {
        val result = StringBuilder()

        // Player attacks first
        val playerAttack = performAttack(player, target)
        result.appendLine(playerAttack)

        // Check if target is dead
        if (target.isDead()) {
            // Remove dead NPC from room
            room.removeItem(target.id)

            // Handle death and loot
            val deathMessage = target.onDeath(player)
            result.append(deathMessage)

            return CommandResult(result.toString())
        }

        // Target counter-attacks if still alive
        val targetAttack = performAttack(target, player)
        result.appendLine(targetAttack)

        // Check if player is dead
        if (player.isDead()) {
            result.appendLine("You have been defeated! Game Over.")
            return CommandResult(result.toString(), shouldQuit = true)
        }

        return CommandResult(result.toString())
    }

    private fun performAttack(
        attacker: com.terheyden.mud.corelib.living.Living,
        defender: com.terheyden.mud.corelib.living.Living,
    ): String {
        val attackPower = attacker.getTotalAttackPower()

        // Add some randomness to combat (±20%)
        val randomFactor = Random.nextDouble(0.8, 1.2)
        val damage = (attackPower * randomFactor).toInt()

        // Apply damage
        val survived = defender.takeDamage(damage)

        // Reduce weapon durability if equipped
        attacker.equippedWeapon?.reduceDurability()

        return buildString {
            if (attacker.equippedWeapon != null) {
                append("${attacker.name} attacks ${defender.name} with ${attacker.equippedWeapon!!.name}")
            } else {
                append("${attacker.name} attacks ${defender.name} with bare hands")
            }

            append(" for $damage damage!")

            if (!survived) {
                append(" ${defender.name} is defeated!")
            } else {
                append(" ${defender.name} is now in ${defender.getHealthDescription()}.")
            }
        }
    }
}

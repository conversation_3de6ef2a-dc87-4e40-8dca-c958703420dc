package com.terheyden.mud.corelib

/**
 * Represents an item the player can interact with.
 *
 * @param isPickupable - Can the player pick this item up?
 * @param currentEnv - The current environment the item is in. If this is an item, this could be
 *     a room, a container, or the player's inventory. If this is a living, this should
 *     be a room. If this is a room, this is null.
 */
open class Item(
    id: String,
    override var name: String,
    override var description: String,
    override var aliases: List<String> = emptyList(),
    var weight: Int = 0,
    var isPickupable: Boolean = false,
    var currentEnv: Container? = null,
) : GameObject(id = id), Visible {

    /**
     * Check if this item matches the given input string.
     * Matches against the item's id, name and aliases (case-insensitive).
     */
    override fun matches(input: String): Boolean {
        val normalizedInput = input.trim().lowercase()

        if (normalizedInput == id) return true
        return super.matches(input)
    }

    /**
     * Get a formatted description for when the item is examined.
     */
    open fun getExamineDescription() = description

    /**
     * Get a short description for when the item is listed.
     */
    open fun getShortDescription() = name

    override fun toString() =
        "Item(" +
                "weight=$weight, " +
                "isPickupable=$isPickupable) " +
                super.toString()
}

package com.terheyden.mud.corelib

import com.terheyden.mud.corelib.GameService.tickService
import com.terheyden.mud.corelib.living.Player
import com.terheyden.mud.corelib.room.Room
import com.terheyden.mud.corelib.room.RoomRegistry
import com.terheyden.mud.corelib.tick.Tickable
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.beans.factory.InitializingBean
import org.springframework.stereotype.Service

/**
 * Initializes the player, rooms, and their contents once they are fully loaded.
 * Sets up and verifies state before the game starts.
 */
@Service
class GameInitService(
    private val player: Player,
    private val rooms: RoomRegistry,
) : InitializingBean {

    private val logger = KotlinLogging.logger {}

    override fun afterPropertiesSet() {
        logger.info { "GameInitService starting initialization." }
        rooms.forEach { initRoom(it) }
        rooms.forEach { tickRoom(it) }
        logger.info { "GameInitService completed initialization." }
    }

    private fun initRoom(room: Room) {
        // Init the room itself:
        room.onInit()

        // Init all initializable objects in the room:
        room.items.filterIsInstance<Initializable>().forEach { it.onInit() }
        room.features.filterIsInstance<Initializable>().forEach { it.onInit() }
        room.doors.values.filterIsInstance<Initializable>().forEach { it.onInit() }
    }

    private fun tickRoom(room: Room) {
        // Register the room for ticks:
        tickService.register(room)

        // Register all tickable objects in the room:
        room.items.filterIsInstance<Tickable>().forEach { tickService.register(it) }
        room.features.filterIsInstance<Tickable>().forEach { tickService.register(it) }
        room.doors.values.filterIsInstance<Tickable>().forEach { tickService.register(it) }
    }
}

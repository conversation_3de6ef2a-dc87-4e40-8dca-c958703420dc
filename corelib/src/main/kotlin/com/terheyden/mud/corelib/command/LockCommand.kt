package com.terheyden.mud.corelib.command

import com.terheyden.mud.corelib.GameService
import org.springframework.stereotype.Component

/**
 * Command to lock doors with keys.
 */
@Component
class LockCommand : Command {
    override val name = "lock"
    override val aliases = listOf("close with")
    override val description = "Lock a door with a key"

    override fun execute(args: List<String>): CommandResult {
        if (args.isEmpty()) {
            return CommandResult("Lock what? Specify a door to lock.")
        }

        val player = GameService.player
        val currentRoom = player.currentRoom

        // Parse arguments - expect "lock door with key" or "lock door key" or just "lock door"
        val argString = args.joinToString(" ")
        val parts = if (argString.contains(" with ")) {
            argString.split(" with ", limit = 2)
        } else {
            // Try to split by finding the last word as the key
            val words = args.toMutableList()
            if (words.size >= 2) {
                val keyName = words.removeLastOrNull() ?: ""
                listOf(words.joinToString(" "), keyName)
            } else {
                listOf(argString, "")
            }
        }

        val doorName = parts[0].trim()
        var keyName = if (parts.size > 1) parts[1].trim() else ""

        // Find the door in the room.
        val door = currentRoom.doors.values.find { it.matches(doorName) }
            ?: return CommandResult("There is no '$doorName' here that can be locked.")

        val requiredKeyId = door.requiredKeyId
        if (requiredKeyId == null) return CommandResult("The ${door.name} is not lockable.")

        // If no key specified, determine the key automatically
        if (keyName.isBlank()) {
            keyName = requiredKeyId
        }

        // Find the key in player's inventory
        val key = player.findInInventory(keyName)
        if (key == null) {
            return CommandResult("You don't have a '$keyName' in your inventory.")
        }

        // Attempt to lock the door
        return CommandResult(door.lock(key))
    }
}

package com.terheyden.mud.corelib.living

import com.terheyden.mud.corelib.Container
import com.terheyden.mud.corelib.Item
import com.terheyden.mud.corelib.room.Room
import com.terheyden.mud.corelib.room.TheVoid
import com.terheyden.mud.corelib.tick.Tickable
import com.terheyden.mud.corelib.weapon.Weapon
import kotlin.math.max
import kotlin.math.min

/**
 * A living entity that can move around the game world (Player, NPCs, etc.)
 */
abstract class Living(
    id: String,
    name: String,
    description: String,
    var currentRoom: Room = TheVoid.Companion.Instance,
    inventory: Collection<Item> = emptyList(),
    maxInventoryItems: Int = 20,
    maxInventoryWeight: Int = 50,
    // Combat-related properties
    var maxHealthPoints: Int = 100,
    var currentHealthPoints: Int = maxHealthPoints,
    var experiencePoints: Int = 0,
    var level: Int = 1,
    var baseAttackPower: Int = 10,
    var baseDefense: Int = 5,
) : Container(
    id = id,
    name = name,
    description = description,
    weight = 0,
    isPickupable = false,
    maxNumItems = maxInventoryItems,
    maxWeight = maxInventoryWeight,
    items = inventory,
), Tickable {

    // Equipment slots
    var equippedWeapon: Weapon? = null
        private set

    /**
     * Move the player to a new room.
     */
    fun moveTo(room: Room) {
        currentRoom = room
    }

    /**
     * Check if the player can carry an additional item.
     */
    fun canCarry(item: Item): Boolean {
        return canAddItem(item)
    }

    /**
     * Add an item to the player's inventory.
     * @return true if successful, false if the player can't carry it
     */
    fun addToInventory(item: Item): Boolean {
        return if (canCarry(item)) {
            addItem(item)
            true
        } else {
            false
        }
    }

    /**
     * Remove an item from the player's inventory.
     */
    fun removeFromInventory(itemId: String): Item? {
        return removeItem(itemId)
    }

    /**
     * Find an item in the player's inventory.
     */
    fun findInInventory(itemId: String): Item? {
        return findItem(itemId)
    }

    /**
     * Get a description of the player's inventory.
     */
    fun getInventoryDescription(): String {
        return if (isEmpty()) {
            "Your inventory is empty."
        } else {
            buildString {
                appendLine("Your inventory contains:")
                items.forEach { item ->
                    appendLine("  - ${item.getShortDescription()}")
                }
                appendLine()
                appendLine("Total weight: ${weightOfItems}/${maxWeight}")
            }
        }
    }

    // Combat-related methods

    /**
     * Check if this living entity is alive.
     */
    fun isAlive(): Boolean = currentHealthPoints > 0

    /**
     * Check if this living entity is dead.
     */
    fun isDead(): Boolean = !isAlive()

    /**
     * Get current health as a percentage.
     */
    fun getHealthPercentage(): Int = (currentHealthPoints * 100) / maxHealthPoints

    /**
     * Get a description of current health status.
     */
    fun getHealthDescription(): String {
        val percentage = getHealthPercentage()
        return when {
            percentage >= 90 -> "excellent health"
            percentage >= 70 -> "good health"
            percentage >= 50 -> "fair health"
            percentage >= 30 -> "poor health"
            percentage >= 10 -> "terrible health"
            else -> "near death"
        }
    }

    /**
     * Calculate total attack power including equipped weapon.
     */
    fun getTotalAttackPower(): Int {
        val weaponDamage = equippedWeapon?.damage ?: 0
        return baseAttackPower + weaponDamage + (level - 1) * 2
    }

    /**
     * Calculate total defense including level bonuses.
     */
    fun getTotalDefense(): Int {
        return baseDefense + (level - 1)
    }

    /**
     * Take damage and return true if still alive.
     */
    open fun takeDamage(damage: Int): Boolean {
        val actualDamage = max(1, damage - getTotalDefense())
        currentHealthPoints = max(0, currentHealthPoints - actualDamage)
        return isAlive()
    }

    /**
     * Heal for the specified amount.
     */
    fun heal(amount: Int) {
        currentHealthPoints = min(maxHealthPoints, currentHealthPoints + amount)
    }

    /**
     * Gain experience points and check for level up.
     */
    fun gainExperience(amount: Int): Boolean {
        experiencePoints += amount
        val experienceNeeded = getExperienceNeededForNextLevel()

        if (experiencePoints >= experienceNeeded) {
            levelUp()
            return true
        }
        return false
    }

    /**
     * Calculate experience needed for next level.
     */
    fun getExperienceNeededForNextLevel(): Int = level * 100

    /**
     * Level up the character.
     */
    private fun levelUp() {
        level++
        val healthIncrease = 20
        maxHealthPoints += healthIncrease
        currentHealthPoints += healthIncrease
        baseAttackPower += 3
        baseDefense += 2
    }

    /**
     * Equip a weapon.
     */
    fun equipWeapon(weapon: Weapon): Boolean {
        if (findInInventory(weapon.id) != null) {
            equippedWeapon = weapon
            return true
        }
        return false
    }

    /**
     * Unequip current weapon.
     */
    fun unequipWeapon(): Weapon? {
        val weapon = equippedWeapon
        equippedWeapon = null
        return weapon
    }

    /**
     * Get combat status description.
     */
    fun getCombatStatus(): String {
        return buildString {
            appendLine("*=== Combat Status ===*")
            appendLine("Health: `$currentHealthPoints/$maxHealthPoints` (${getHealthDescription()})")
            appendLine("Level: _${level}_")
            appendLine("Experience: `$experiencePoints/${getExperienceNeededForNextLevel()}`")
            appendLine("Attack Power: _${getTotalAttackPower()}_")
            appendLine("Defense: _${getTotalDefense()}_")
            if (equippedWeapon != null) {
                appendLine("Equipped Weapon: *${equippedWeapon!!.name}* (`${equippedWeapon!!.damage}` damage)")
            } else {
                appendLine("Equipped Weapon: _None_ (fighting with bare hands)")
            }
        }
    }

    // === TICKABLE IMPLEMENTATION ===

    /**
     * Called on each game tick for regeneration and autonomous behavior.
     */
    override fun onTick(tickCount: Long) {
        // Health regeneration - heal 1 HP every 10 ticks if alive and not at full health
        if (isAlive() && currentHealthPoints < maxHealthPoints && tickCount % 10 == 0L) {
            heal(1)
        }

        // Call subclass-specific tick behavior
        onTickBehavior(tickCount)
    }

    /**
     * Override this method in subclasses for custom tick behavior.
     * Called after standard regeneration processing.
     */
    open fun onTickBehavior(tickCount: Long) {
        // Default implementation does nothing
    }

    /**
     * Living entities are ticked every 5 ticks (slower than every tick for performance).
     */
    override fun getTickInterval(): Int = 5

    /**
     * Only tick if the entity is alive.
     */
    override fun isTickEnabled(): Boolean = isAlive()
}

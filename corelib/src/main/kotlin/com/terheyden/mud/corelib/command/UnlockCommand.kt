package com.terheyden.mud.corelib.command

import com.terheyden.mud.corelib.GameService
import org.springframework.stereotype.Component

/**
 * Command to unlock doors with keys.
 */
@Component
class UnlockCommand : Command {
    override val name = "unlock"
    override val aliases = listOf("open with")
    override val description = "Unlock a door with a key"

    override fun execute(args: List<String>): CommandResult {
        if (args.isEmpty()) {
            return CommandResult("Unlock what? Specify a door to unlock.")
        }

        val player = GameService.player
        val currentRoom = player.currentRoom

        // Parse arguments - expect "unlock door with key" or "unlock door key"
        val argString = args.joinToString(" ")
        val parts = if (argString.contains(" with ")) {
            argString.split(" with ", limit = 2)
        } else {
            // Try to split by finding the last word as the key
            val words = args.toMutableList()
            if (words.size >= 2) {
                val keyName = words.removeLastOrNull() ?: ""
                listOf(words.joinToString(" "), keyName)
            } else {
                listOf(argString, "")
            }
        }

        val doorName = parts[0].trim()
        var keyName = if (parts.size > 1) parts[1].trim() else ""

        // Find the door in the room.
        val door = currentRoom.doors.values.find { it.matches(doorName) }
            ?: return CommandResult("There is no '$doorName' here that can be unlocked.")

        val requiredKeyId = door.requiredKeyId
        if (requiredKeyId == null) return CommandResult("The ${door.name} is not unlockable.")

        // If no key specified, determine the key automatically
        if (keyName.isBlank()) {
            keyName = requiredKeyId
        }

        // Find the key in player's inventory
        val key = player.findInInventory(keyName)
        if (key == null) {
            return CommandResult("You don't have a '$keyName' in your inventory.")
        }

        // Attempt to unlock the door
        return CommandResult(door.unlock(key))
    }
}

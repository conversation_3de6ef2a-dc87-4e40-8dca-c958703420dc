package com.terheyden.mud.corelib.command

import com.terheyden.mud.corelib.GameService
import com.terheyden.mud.corelib.living.NPCType
import org.springframework.stereotype.Component

/**
 * Command to rest and recover health and energy.
 */
@Component
class RestCommand : Command {
    override val name = "rest"
    override val aliases = listOf("sleep", "recover", "heal")
    override val description = "Rest to recover health and energy"

    override fun execute(args: List<String>): CommandResult {
        val player = GameService.player
        val currentRoom = player.currentRoom

        // Check if there are hostile NPCs in the room
        val hostileNPCs = currentRoom.contentsNPCs.filter {
            it.isAlive() && it.npcType == NPCType.AGGRESSIVE
        }

        if (hostileNPCs.isNotEmpty()) {
            return CommandResult(
                "You cannot rest while there are hostile creatures nearby! " +
                        "Deal with the ${hostileNPCs.joinToString(", ") { it.name }} first."
            )
        }

        // Check if player is already at full health
        if (player.currentHealthPoints >= player.maxHealthPoints) {
            return CommandResult("You are already at full health and don't need to rest.")
        }

        // Calculate healing amount based on room safety and features
        val baseHealing = player.maxHealthPoints / 4 // 25% of max health
        val roomBonus = getRoomHealingBonus(currentRoom)
        val totalHealing = baseHealing + roomBonus

        // Apply healing
        val oldHealth = player.currentHealthPoints
        player.heal(totalHealing)
        val actualHealing = player.currentHealthPoints - oldHealth

        return CommandResult(buildString {
            appendLine("You find a comfortable spot and rest for a while, allowing your body to recover.")
            appendLine("You feel refreshed and restored!")
            appendLine("Health restored: $actualHealing points (${player.currentHealthPoints}/${player.maxHealthPoints})")

            if (roomBonus > 0) {
                appendLine("The peaceful nature of this place enhances your recovery.")
            }
        })
    }

    private fun getRoomHealingBonus(room: com.terheyden.mud.corelib.room.Room): Int {
        // Different rooms provide different healing bonuses
        return when (room.id) {
            "village_inn" -> 20 // Inns are great for resting
            "village_temple" -> 15 // Temples provide spiritual healing
            "enchanted_grove" -> 25 // Magical places enhance healing
            "village_square" -> 10 // Safe, civilized areas
            "forest_clearing" -> 5 // Natural areas provide some bonus
            "tower_chamber" -> 5 // Magical places
            else -> 0 // No bonus for dangerous or neutral areas
        }
    }
}
